import axios from '@/core/api/axios.js'
//@竞赛相关请求

// 校验赛事编号唯一性
export function checkCompetitionCodeUnique(code) {
    return axios.get('/api/competition/checkCodeUnique', { params: { code } });
}

// 新增比赛
export function addCompetition(data) {
    return axios.post('/api/competition/add', data);
}

// 更新比赛信息
export function competitionUpdateApi(data) {
    return axios.post('/competition/update', data);
}

// 删除比赛
export function competitionDelApi(id) {
    return axios.post('/api/competition/delete', { id });
}

// 分页获取比赛列表
export function competitionPageApi(params) {
    return axios.get('/api/competition/list', { params });
}

// 获取比赛详情
export function competitionGetApi(id) {
    return axios.get('/api/competition/detail', { params: { id } });
}

// 获取比赛列表（可选，若有批量操作需求）
export function getCompetitionList(params) {
    return axios.get('/api/competition/list', { params });
}

// 获取比赛详情（可选，按code查询）
export function getCompetitionDetail(code) {
    return axios.get('/api/competition/detail', { params: { code } });
}

// 批量修改比赛状态
export function batchUpdateStatusApi(data) {
    return axios.post('/api/competition/batchUpdateStatus', data);
}

// 批量删除比赛
export function batchDeleteApi(data) {
    return axios.post('/api/competition/batchDelete', data);
}

// 上传赛事官方文件（分片）
export function uploadOfficialFile(formData) {
    // 必须return，确保Promise返回
    return axios.post('/api/competition/uploadOfficialFile', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
    });
}