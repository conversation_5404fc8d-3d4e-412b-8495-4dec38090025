import axios from '@/core/api/axios.js'
import request from '@/core/api/axios_config'
//@学生相关请求
//个人信息填写模块
/**
 * @description 获取当前登录用户的学生档案信息
 * @param {string} studentId - 学号
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function getStudentProfile(studentId) {
  return axios.get('/api/competition/student/profile', { params: { studentId } })
}


/**
 * @description 新增或更新当前登录用户的学生档案信息
 * @param {object} data - 包含学生信息的对象
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function submitStudentProfile(data) {
  return axios.post('/api/competition/student/add', data)
}

/**
 * @description 获取学校列表
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function getSchoolList() {
  return axios.get('/api/common/schools')
}

// 获取院系列表（已完成）
export function getDepartmentList(schoolId) {
  return axios.get('/api/common/department', { params: { school_id: schoolId } })
}

// 获取专业列表（已完成）
export function getMajorList(schoolId, departmentName) {
  return axios.get('/api/common/majors', { params: { school_id: schoolId, department_name: departmentName } })
}




