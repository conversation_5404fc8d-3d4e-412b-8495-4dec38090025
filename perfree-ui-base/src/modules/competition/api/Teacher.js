import axios from '@/core/api/axios.js'

/**
 * 更新教师个人信息
 * @param {Object} data 教师信息表单数据，需包含 userId、name、work_id、phone、email、education、entry_year、position、department、work_photo
 * @returns {Promise} 请求Promise对象
 */
export function submitTeacherProfile(data) {
  return axios.post('/api/competition/teacher/update', data)
}

/**
 * 获取教师个人信息
 * @param {number} userId - 用户ID
 * @returns {Promise} 请求Promise对象
 */
export function getTeacherProfile(userId) {
  return axios.get('/api/competition/teacher/get', { params: { userId } })
}

// 获取教师工作证照片访问路径
export function getTeacherWorkPhotoUrl(userId) {
  return `/api/competition/teacher/getTWP/${userId}`
}


