import axios from '@/core/api/axios.js'

export const workDetailsApi = {
  getWorkListByUserId: (userId, params) => {
    return axios.get(`/api/competition/work/user/${userId}`, { params })
  },

  getWorkDetail: (id) => {
    return axios.get(`/api/competition/work/detail/${id}`)
  },

  updateWork: (id, data) => {
    return axios.put(`/api/competition/work/update/${id}`, data)
  },

  deleteWork: (id) => {
    return axios.delete(`/api/competition/work/delete/${id}`)
  },

  submitWork: (id) => {
    return axios.post(`/api/competition/work/submit/${id}`)
  },

  exitCompetition: (id) => {
    return axios.post(`/api/competition/work/exit/${id}`)
  },

  updateParticipants: (id, data) => {
    return axios.put(`/api/competition/work/participants/${id}`, data)
  },

  updateWorkInfo: (id, data) => {
    return axios.put(`/api/competition/work/info/${id}`, data)
  },

  updateInstructors: (id, data) => {
    return axios.put(`/api/competition/work/instructors/${id}`, data)
  },

  downloadWorkFiles: (id) => {
    return axios.get(`/api/competition/work/download/${id}`, {
      responseType: 'blob'
    })
  },

  getCompetitionTypes: () => {
    return axios.get('/api/competition/types')
  },

  getProjectTypes: () => {
    return axios.get('/api/competition/project-types')
  },

  getTeamSizeOptions: () => {
    return axios.get('/api/competition/team-sizes')
  },

  getRoleOptions: () => {
    return axios.get('/api/competition/roles')
  }
}

export const workDataUtils = {
  transformWorkList: (data) => {
    return data.map(item => ({
      id: item.id,
      code: item.code || item.entryCode,
      name: item.name || item.title,
      competitionCode: item.competitionCode || item.competitionId,
      competitionName: item.name,
      type: item.type,
      authors: item.authors || item.participants || [],
      instructors: item.instructors || item.teamLeaders || [],
      status: item.status || 'draft',
      submitTime: item.submitTime,
      abstract: item.abstract,
      files: item.files || [],
      createTime: item.createTime,
      updateTime: item.updateTime,
      userId: item.userId
    }))
  },

  transformWorkDetail: (data) => {
    return {
      id: data.id,
      code: data.code,
      name: data.name,
      competitionCode: data.competitionCode,
      competitionName: data.name,
      type: data.type,
      authors: data.authors || [],
      instructors: data.instructors || [],
      status: data.status,
      submitTime: data.submitTime,
      abstract: data.abstract,
      files: data.files || [],
      projectPlan: data.projectPlan,
      videoLink: data.videoLink,
      videoCode: data.videoCode,
      createTime: data.createTime,
      updateTime: data.updateTime
    }
  },

  transformParticipants: (data) => {
    return data.map(item => ({
      id: item.id,
      name: item.name,
      role: item.role,
      studentId: item.studentId,
      phone: item.phone,
      email: item.email
    }))
  },

  transformInstructors: (data) => {
    return data.map(item => ({
      id: item.id,
      name: item.name,
      role: item.role,
      unit: item.unit,
      contact: item.contact,
      email: item.email
    }))
  }
}

export const statusMap = {
  draft: { label: '草稿', type: 'info' },
  submitted: { label: '已提交', type: 'warning' },
  reviewing: { label: '评审中', type: 'primary' },
  approved: { label: '已通过', type: 'success' },
  rejected: { label: '已驳回', type: 'danger' }
}

export const typeMap = {
  academic: { label: '学术竞赛', type: 'primary' },
  sports: { label: '体育赛事', type: 'success' },
  innovation: { label: '创新创业', type: 'warning' },
  art: { label: '文化艺术', type: 'info' }
}

export const roleMap = {
  leader: { label: '队长', type: 'primary' },
  member: { label: '队员', type: 'info' },
  instructor: { label: '指导教师', type: 'success' },
  assistant: { label: '辅助教师', type: 'warning' }
}
