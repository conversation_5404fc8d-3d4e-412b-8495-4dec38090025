import axios from '@/core/api/axios.js'

// 获取赛事可视化分页数据
export function getVisualizationPage(params) {
  return axios.post('/api/competition/getPage', {
    pageNo: params.pageNo || 1,
    pageSize: params.pageSize || 6,
    name: params.name || '',
    code: params.code || '',
    type: params.type || ''
  })
}

// 获取比赛详情（按code查询）
export function getCompetitionDetail(code) {
  return axios.get('/api/competition/get', { params: { code } })
}

// 查询当前用户是否已报名某赛事
export function getEntryStatus(params) {
  return axios.get('/api/competition/entry/status', { params })
}

// 个人赛报名接口
export function registerIndividualEntry(data) {
  return axios.post('/api/competition/entry/register', data)
}

// 团体赛报名接口
export function registerTeamEntry(data) {
  return axios.post('/api/competition/enrollCompetition', data)
}

// 创建团队
export function createTeam(data) {
  return axios.post('/api/competition/team/create_team', {
    userId: data.userId,
    name: data.name,
    description: data.description
  })
}

// 获取队长团队列表
export function getCaptainTeam(params) {
  return axios.post('/api/competition/team/getCaptainTeam', params)
}

// 获取赛事官方文件列表
export function getCompetitionFileList(competitionCode) {
  return axios.get('/api/competition/getFileList', { params: { competitionCode } })
}
