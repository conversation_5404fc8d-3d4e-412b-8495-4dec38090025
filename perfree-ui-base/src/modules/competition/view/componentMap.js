export default {
  'StudentProfileForm': () => import('./StudentProfileForm.vue'),
  'TeacherProfileForm': () => import('./TeacherProfileForm.vue'),
  'CompetitionManage': () => import('./CompetitionManage.vue'),
  'CompetitionAppeal': () => import('./CompetitionAppeal.vue'),
  'CompetitionAppealReview': () => import('./CompetitionAppealReview.vue'),
  'CompetitionEntryDetails': () => import('./CompetitionEntryDetails.vue'),
  'TeamManage': () => import('./TeamManage.vue'),
  'CompetitionOrganizer': () => import('./CompetitionOrganizer.vue'),
  'CompetitionVisualization': () => import('./CompetitionVisualization.vue'),
  // 'WorksManagement': () => import('./WorksManagement.vue'),
  'CompetitionWork': () => import('./CompetitionWork.vue'),
  // 'CompetitionStudentProfile':()=>import('./CompetitionStudentProfile.vue'),
  // 'IntelligentTeamMatching':()=>import('./IntelligentTeamMatching.vue'),
  // 'CompetitionSmartPush':()=>import('./CompetitionSmartPush.vue'),
  // 'CompetitionProgressDashboard':()=>import('./CompetitionProgressDashboard.vue' ),
  // 'CompetitionAIAssistant':()=>import('./CompetitionAIAssistant.vue'),
  // 'CustomService':()=>import('./CustomService.vue'),
  // 'EmailMessage':()=>import('./EmailMessage.vue')
}