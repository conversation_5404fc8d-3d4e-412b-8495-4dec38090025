<template>
  <div class="customer-service-page">
    <el-card class="customer-service-card">
      <div class="header">
        <span class="title">
          <el-icon style="vertical-align: middle; margin-right: 6px;"><ChatDotRound /></el-icon>
          智能客服助手-小智
        </span>
        <div class="header-actions">
          <el-button
              :type="deepSearch ? 'success' : 'info'"
              @click="toggleDeepSearch"
              class="custom-btn deep-search-btn"
              :disabled="sending"
          >
            <el-icon :size="14" style="margin-right: 4px;"><Search /></el-icon>
            {{ deepSearch ? '深度搜索' : '普通搜索' }}
          </el-button>
          <el-button @click="clearChat" class="custom-btn clear-btn">清空</el-button>
          <el-button @click="showHistoryDialog" class="custom-btn history-btn">
            <el-icon :size="14" style="margin-right: 4px;"><Document /></el-icon>
            查询历史对话
          </el-button>
        </div>
      </div>
      <el-divider />
      <div class="chat-main">
        <div class="chat-messages" ref="messageContainer">
          <transition-group name="message-fade" tag="div">
            <div v-for="message in messages" :key="message.id" :class="['message', message.type === 'user' ? 'user-message' : 'assistant-message']">
              <el-avatar :size="40" :src="message.type === 'user' ? userAvatar : assistantAvatar" class="message-avatar" />
              <div class="message-content">
                <div class="message-text">{{ message.content }}</div>
                <div class="message-time">
                  {{ message.time }}
                  <span v-if="message.isError" @click="retryMessage(message)" class="retry-btn">（点击重试）</span>
                </div>
              </div>
            </div>
          </transition-group>
        </div>
        <div class="chat-bottom-bar">
          <div class="chat-bottom-bar-row">
            <div class="quick-questions">
              <span class="quick-title">常见问题：</span>
              <el-tag v-for="(question, index) in quickQuestions" :key="index" class="question-tag" size="small" @click="selectQuickQuestion(question)" :disabled="sending">
                {{ question }}
              </el-tag>
            </div>
            <div class="btns-add">
              <button>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
                  <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8v8a5 5 0 1 0 10 0V6.5a3.5 3.5 0 1 0-7 0V15a2 2 0 0 0 4 0V8"></path>
                </svg>
              </button>
              <button>
                <svg viewBox="0 0 24 24" height="20" width="20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4 5a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1zm0 10a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1zm10 0a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0-8h6m-3-3v6" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke="currentColor" fill="none"></path>
                </svg>
              </button>
              <button @click="toggleDeepSearch">
                <svg viewBox="0 0 24 24" height="20" width="20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10m-2.29-2.333A17.9 17.9 0 0 1 8.027 13H4.062a8.01 8.01 0 0 0 5.648 6.667M10.03 13c.151 2.439.848 4.73 1.97 6.752A15.9 15.9 0 0 0 13.97 13zm9.908 0h-3.965a17.9 17.9 0 0 1-1.683 6.667A8.01 8.01 0 0 0 19.938 13M4.062 11h3.965A17.9 17.9 0 0 1 9.71 4.333A8.01 8.01 0 0 0 4.062 11m5.969 0h3.938A15.9 15.9 0 0 0 12 4.248A15.9 15.9 0 0 0 10.03 11m4.259-6.667A17.9 17.9 0 0 1 15.973 11h3.965a8.01 8.01 0 0 0-5.648-6.667" fill="currentColor"></path>
                </svg>
              </button>
            </div>
          </div>
          <div class="chat-input-row">
            <el-input ref="inputRef" v-model="inputMessage" type="textarea" :rows="2" :disabled="sending" placeholder="按 Enter 发送，Shift + Enter 换行" @keydown.enter.exact.prevent="sendMessage" @keydown.enter.shift.exact.prevent="handleNewLine" class="chat-input" />
            <button class="send-btn-icon" @click="sendMessage" :disabled="sending || !inputMessage.trim()">
              <i>
                <svg viewBox="0 0 512 512">
                  <path fill="currentColor"
                        d="M473 39.05a24 24 0 0 0-25.5-5.46L47.47 185h-.08a24 24 0 0 0 1 45.16l.41.13l137.3 58.63a16 16 0 0 0 15.54-3.59L422 80a7.07 7.07 0 0 1 10 10L226.66 310.26a16 16 0 0 0-3.59 15.54l58.65 137.38c.06.2.12.38.19.57c3.2 9.27 11.3 15.81 21.09 16.25h1a24.63 24.63 0 0 0 23-15.46L478.39 64.62A24 24 0 0 0 473 39.05">
                  </path>
                </svg>
              </i>
            </button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 历史对话侧边栏 -->
    <el-drawer
        v-model="historyDrawerVisible"
        title="历史对话记录"
        size="400px"
        :destroy-on-close="true"
        class="history-drawer"
    >
      <div class="history-search">
        <el-input
            v-model="historySearchKeyword"
            placeholder="搜索历史对话"
            clearable
            @input="filterHistoryMessages"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="history-list">
        <div
            v-for="(chat, index) in filteredHistoryMessages"
            :key="index"
            class="history-item"
            @click="loadHistoryChat(chat)"
        >
          <div class="history-item-header">
            <span class="history-item-time">{{ chat.time }}</span>
            <el-button
                type="danger"
                size="small"
                circle
                @click.stop="deleteHistoryChat(index)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <div class="history-item-content">
            <div class="history-item-preview">
              {{ chat.preview }}
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import { ChatDotRound, Search, Document, Delete } from '@element-plus/icons-vue'
import useChat from './CommonView/UserChat'

const inputMessage = ref('')
const inputRef = ref(null)
const messageContainer = ref(null)

const userAvatar = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
const assistantAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

const quickQuestions = [
  '如何报名比赛？',
  '比赛时间安排',
  '参赛资格要求',
  '奖项设置',
  '如何提交作品？',
  '比赛规则说明'
]


const {
  messages,
  sending,
  deepSearch,
  historyDrawerVisible,
  historySearchKeyword,
  historyMessages,
  filteredHistoryMessages,
  saveToHistory,
  loadHistoryChat,
  deleteHistoryChat,
  showHistoryDialog,
  sendMessage: useChatSendMessage,
  retryMessage,
  toggleDeepSearch,
  clearChat,
  filterHistoryMessages
} = useChat()


const sendMessage = async () => {
  await useChatSendMessage(inputMessage, userAvatar, assistantAvatar, scrollToBottom, () => {
    inputRef.value?.focus()
  })
}

const selectQuickQuestion = (question) => {
  inputMessage.value = question
  sendMessage()
}

const handleNewLine = () => {
  inputMessage.value += '\n'
}

const scrollToBottom = async () => {
  await nextTick();
  setTimeout(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight;
    }
  }, 60);
}

watch(messages, async () => {
  await nextTick();
  await scrollToBottom();
}, { flush: 'post' });

watch(inputMessage, async () => {
  await nextTick();
  await scrollToBottom();
});
</script>

<style scoped>
.customer-service-page, .customer-service-card {
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.customer-service-card {
  flex: 1;
  min-height: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 16px !important;
  margin: 1px !important;
  box-shadow: none;
  border: none;
  width: 100%;
}
:deep(.el-card__body) {
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 0;
}
.header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  font-size: 20px;
  font-weight: 600;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px 0 rgba(64,158,255,0.06);
  border-bottom: 1px solid #f0f0f0;
}
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
.el-divider {
  border-color: #f0f0f0 !important;
  margin: 0 !important;
}
.chat-main {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.chat-messages {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  padding: 24px 0;
  background: #fff;
}
.chat-bottom-bar {
  flex-shrink: 0;
  background: #fff;
  box-shadow: 0 -2px 16px rgba(64,158,255,0.08);
  border-top: 1px solid #f2f3f5;
  padding: 8px 0 0 0;
  position: relative;
}
.message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 0 24px;
  transition: all 0.3s;
}
.user-message {
  flex-direction: row-reverse;
  padding-right: 20px;
  padding-left: 100px;
}
.assistant-message {
  flex-direction: row;
  padding-left: 20px;
  padding-right: 100px;
}
.message-content {
  max-width: 75%;
  margin: 0 16px;
  position: relative;
  font-size: 16px;
  line-height: 1.6;
  word-break: break-word;
}
.message-text {
  padding: 14px 18px;
  border-radius: 14px;
  font-size: 16px;
  line-height: 1.6;
  word-break: break-word;
}
.user-message .message-text {
  background: #409eff;
  color: #fff;
  border-top-right-radius: 4px;
  margin-left: auto;
}
.assistant-message .message-text {
  background: #f5f7fa;
  color: #333;
  border-top-left-radius: 4px;
  margin-right: auto;
}
.message-time {
  font-size: 13px;
  color: #909399;
  margin-top: 8px;
  padding: 0 4px;
}
.user-message .message-time {
  text-align: right;
}
.assistant-message .message-time {
  text-align: left;
}
.el-avatar {
  width: 44px;
  height: 44px;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  flex-shrink: 0;
  background: #fff;
}
.chat-input-row {
  display: flex;
  align-items: stretch;
  gap: 10px;
  margin: 0 24px 12px 24px;
}
.chat-input {
  flex: 1;
}
:deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 1.5px solid #f2f3f5;
  font-size: 15px;
  background: #fafcff;
  transition: border 0.2s, box-shadow 0.2s;
  min-height: 40px !important;
  max-height: 70px !important;
  box-shadow: none;
  padding: 10px 16px;
  resize: none;
}
:deep(.el-textarea__inner:focus) {
  border: 1.5px solid #409eff !important;
  box-shadow: 0 0 0 2px #eaf6ff;
}
:deep(.el-textarea__inner::placeholder) {
  color: #bfcbd9;
}
.custom-btn {
  height: 36px;
  padding: 0 20px;
  font-size: 15px;
  border-radius: 18px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px 0 rgba(64,158,255,0.08);
  background: #f7faff;
  color: #409eff;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  margin-left: 8px;
  display: flex;
  align-items: center;
}
.custom-btn:hover {
  background: #eaf6ff;
  color: #337ecc;
  box-shadow: 0 4px 16px 0 rgba(64,158,255,0.12);
}
.deep-search-btn {
  background: linear-gradient(90deg, #e0eaff 60%, #f7faff 100%);
  color: #409eff;
}
.deep-search-btn.success {
  background: linear-gradient(90deg, #409eff 60%, #66b1ff 100%);
  color: #fff;
}
.send-btn-icon {
  height: 100%;
  min-height: 64px;
  width: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: 8px;
  background: #409eff;
  box-shadow: 0 0 16px 4px rgba(64,158,255,0.18), 0 0 0 0 #fff;
  border: none;
  outline: none;
  cursor: pointer;
  transition: box-shadow 0.2s, background 0.2s, transform 0.2s;
  position: relative;
}
.send-btn-icon:hover, .send-btn-icon:focus {
  background: #409eff;
  box-shadow: 0 0 32px 8px rgba(64,158,255,0.22), 0 0 0 0 #fff;
  transform: translateY(-2px) scale(1.04);
}
.send-btn-icon i {
  width: 52px;
  height: 52px;
  padding: 0;
  background: transparent;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.send-btn-icon svg {
  width: 38px;
  height: 38px;
  color: #fff;
  transition: all 0.3s ease;
}
.send-btn-icon:hover svg {
  color: #f3f6fd;
  filter: drop-shadow(0 0 5px #ffffff);
}
.send-btn-icon:focus svg {
  color: #f3f6fd;
  filter: drop-shadow(0 0 5px #ffffff);
  transform: scale(1.2) rotate(45deg) translateX(-2px) translateY(1px);
}
.send-btn-icon:active {
  transform: scale(0.92);
}
.send-btn-icon[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}
.clear-btn {
  min-width: 60px;
  border-radius: 10px;
  font-weight: 500;
  color: #888;
  background: #f7faff;
  border: 1px solid #eaf3ff;
  transition: background 0.2s, border-color 0.2s;
}
.clear-btn:hover {
  background: #f0f6ff;
  border-color: #b3d8fd;
  color: #337ecc;
}
.quick-questions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
  margin: 0 24px 8px 24px;
  font-size: 14px;
}
.quick-title {
  margin-right: 8px;
  color: #888;
  font-size: 15px;
  font-weight: bold;
  letter-spacing: 1px;
}
.question-tag {
  margin-right: 8px;
  cursor: pointer;
  background: #f7faff;
  color: #409eff;
  border: 1px solid #eaf3ff;
  border-radius: 16px;
  font-size: 14px;
  padding: 7px 18px;
  line-height: 1.6;
  transition: all 0.2s;
  user-select: none;
}
.question-tag:hover {
  background: #eaf6ff;
  color: #337ecc;
  border-color: #b3d8fd;
  transform: translateY(-4px) scale(1.06);
  box-shadow: 0 4px 16px 0 rgba(64,158,255,0.12);
}
.history-btn {
  background: #f7faff;
  color: #409eff;
}
.history-btn:hover {
  background: #eaf6ff;
  color: #337ecc;
}
.message-fade-enter-active,
.message-fade-leave-active {
  transition: all 0.3s ease;
}
.message-fade-enter-from,
.message-fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
.retry-btn {
  color: #f56c6c;
  cursor: pointer;
  text-decoration: underline;
  margin-left: 4px;
}
.message-avatar {
  transition: transform 0.2s;
}
.message-avatar:hover {
  transform: scale(1.05);
}
@media (max-width: 768px) {
  .header, .chat-bottom-bar, .chat-main {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  .message-content {
    font-size: 14px;
    padding: 12px 10px;
  }
  .send-btn-icon {
    font-size: 13px;
    padding: 0 10px;
    height: 32px;
  }
  .el-avatar {
    width: 32px;
    height: 32px;
  }
}

/* 添加历史对话相关样式 */
.history-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.history-search {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.history-list {
  padding: 16px;
  height: calc(100% - 120px);
  overflow-y: auto;
}

.history-item {
  padding: 16px;
  border-radius: 8px;
  background: #f7faff;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #eaf3ff;
}

.history-item:hover {
  background: #eaf6ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64,158,255,0.1);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-item-time {
  font-size: 13px;
  color: #909399;
}

.history-item-preview {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

:deep(.el-drawer__body) {
  padding: 0;
  height: 100%;
  overflow: hidden;
}


.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: #e0e0e0;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

/* 样式追加 */
.btns-add {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-right: 16px;
}
.btns-add button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
  border: none;
  border-radius: 14px;
  color: rgba(255,255,255,0.7);
  cursor: pointer;
  transition:
      color 0.2s,
      box-shadow 0.3s,
      transform 0.2s,
      background 0.3s;
  box-shadow: 0 2px 8px 0 rgba(64,158,255,0.08);
  font-size: 18px;
  outline: none;
  padding: 0;
  position: relative;
  z-index: 1;
}
.btns-add button:hover {
  color: #fff;
  transform: translateY(-6px) scale(1.10);
  box-shadow: 0 0 16px 2px #fff, 0 4px 24px 0 rgba(64,158,255,0.18);
  filter: brightness(1.08);
}
.btns-add button:active {
  transform: scale(0.96);
}
.btns-add svg {
  width: 22px;
  height: 22px;
  transition: color 0.2s, filter 0.2s;
}
.btns-add button:hover svg {
  color: #fff;
  filter: drop-shadow(0 0 6px #fff);
}

.chat-bottom-bar-row {
  display: flex;
  align-items: center;
  padding: 0 24px 8px 24px;
}


.btns-add button:nth-child(1) {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
}
.btns-add button:nth-child(2) {
  background: linear-gradient(135deg, #b0bec5 0%, #78909c 100%);
}
.btns-add button:nth-child(3) {
  background: linear-gradient(135deg, #b39ddb 0%, #90caf9 100%);
}
</style>