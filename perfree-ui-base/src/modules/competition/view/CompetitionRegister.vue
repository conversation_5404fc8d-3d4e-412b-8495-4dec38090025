<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import CompetitionInfoCard from './CompetitionDetails/CompetitionInfoCard.vue'
import CompetitionDetailPanel from './CompetitionDetails/CompetitionDetailPanel.vue'
import TeamMemberForm from './CompetitionDetails/TeamMemberForm.vue'
import ProUploadFile from './CompetitionDetails/../CommonUpload/ProUploadFile.vue'

const competition = reactive({
  name: '全国大学生创新创业大赛',
  code: 'COMP20250619001',
  organizer: '教育部',
  level: 'national',
  status: 'signup',
  signupStart: '2025-06-01',
  signupEnd: '2025-08-19',
  startTime: '2025-06-19',
  endTime: '2025-10-24',
  description: '本赛事旨在激发大学生创新创业热情，提升实践能力，推动产学研结合。',
  rules: '1. 参赛对象为全国高校在校生；2. 需提交创新创业项目计划书；3. 具体规则详见官方文件。',
  stages: [
    { name: '初赛报名', type: 'signup', typeLabel: '报名', time: '2025-06-01 ~ 2025-08-19', desc: '在线报名，提交材料' },
    { name: '初赛', type: 'preliminary', typeLabel: '初赛', time: '2025-06-19', desc: '线上评审' },
    { name: '决赛', type: 'final', typeLabel: '决赛', time: '2025-10-24', desc: '现场答辩' }
  ],
  officialFiles: [
    { name: '赛事通知.pdf', url: '/files/notice.pdf', uid: '1' }
  ]
})

const form = reactive({
  name: '',
  studentId: '',
  phone: '',
  email: '',
  college: '',
  grade: '',
  members: [],
  entryFile: '',
  agreement: false
})

const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  studentId: [{ required: true, message: '请输入学号', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  college: [{ required: true, message: '请输入学院', trigger: 'blur' }],
  grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
  entryFile: [{ required: true, message: '请上传作品文件', trigger: 'change' }],
  agreement: [{ required: true, validator: (_, v, cb) => v ? cb() : cb('请同意报名须知'), trigger: 'change' }]
}

const formRef = ref()
const loading = ref(false)
const fileList = ref([])
const uploadUrl = '/api/competition/entry/upload'

watch(fileList, (val) => {
  if (val && val.length > 0 && val[0].url) {
    form.entryFile = val[0].url
  } else {
    form.entryFile = ''
  }
})

function handleUploadSuccess(response, file, fileListArr) {
  if (response.code === 200) {
    form.entryFile = response.data
    fileList.value = fileListArr
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}
function handleUploadRemove() {
  form.entryFile = ''
  fileList.value = []
}

function submitForm() {
  formRef.value.validate(async valid => {
    if (!valid) return
    loading.value = true
    try {
      setTimeout(() => {
        ElMessage.success('报名成功！')
        loading.value = false
      }, 1200)
    } catch (e) {
      ElMessage.error('报名失败，请重试')
      loading.value = false
    }
  })
}
</script>

<template>
  <div class="register-bg">
    <div class="center-content">
      <el-card class="register-card" shadow="never">
        <el-row :gutter="24">
          <el-col :span="24">
            <CompetitionInfoCard :competition="competition" />
          </el-col>
        </el-row>
        <el-row :gutter="24" class="register-content-row">
          <el-col :xs="24" :md="14">
            <CompetitionDetailPanel :competition="competition" />
          </el-col>
          <el-col :xs="24" :md="10">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="register-form">
              <div class="form-section-title">个人信息</div>
              <el-form-item label="姓名" prop="name">
                <el-input v-model="form.name" placeholder="请输入真实姓名" />
              </el-form-item>
              <el-form-item label="学号" prop="studentId">
                <el-input v-model="form.studentId" placeholder="请输入学号" />
              </el-form-item>
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="form.phone" placeholder="请输入手机号" maxlength="11" show-word-limit />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item label="学院" prop="college">
                <el-input v-model="form.college" placeholder="请输入学院" />
              </el-form-item>
              <el-form-item label="年级" prop="grade">
                <el-select v-model="form.grade" placeholder="请选择年级">
                  <el-option label="大一" value="大一" />
                  <el-option label="大二" value="大二" />
                  <el-option label="大三" value="大三" />
                  <el-option label="大四" value="大四" />
                  <el-option label="研究生" value="研究生" />
                </el-select>
              </el-form-item>
              <TeamMemberForm v-model="form.members" />
              <el-form-item label="作品文件" prop="entryFile">
                <ProUploadFile
                  v-model:modelValue="fileList"
                  :action="uploadUrl"
                  :accept="'.pdf,.doc,.docx,.zip,.rar'"
                  :limit="1"
                  :max-size="20"
                  size-unit="MB"
                  :show-file-list="true"
                  :disabled="loading"
                  :drag="false"
                  :tip="'支持PDF、Word、压缩包，最大20MB'"
                  @success="handleUploadSuccess"
                  @remove="handleUploadRemove"
                />
              </el-form-item>
              <el-form-item prop="agreement">
                <el-checkbox v-model="form.agreement">
                  我已阅读并同意 <a href="#" target="_blank">《报名须知》</a>
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" :loading="loading" @click="submitForm" size="large" style="width: 100%;">提交报名</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.register-bg {
  background: #fff;
  min-height: 100vh;
  padding: 0;
}
.center-content {
  max-width: 100%;
  margin: 0;
  padding: 0;
}
.register-card {
  border-radius: 0;
  border: none;
  background: #fff;
  box-shadow: none;
  padding: 32px 0 24px 0;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 0;
}
.register-content-row {
  margin-top: 24px;
}
.register-form {
  margin-top: 0;
  background: transparent;
  padding: 0;
  width: 100%;
}
.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 10px;
}
</style>