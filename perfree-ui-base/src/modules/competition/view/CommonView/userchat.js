import { ref, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

let messageId = 0

export default function useChat() {
    const messages = ref([
        {
            id: messageId++,
            type: 'assistant',
            content: '您好！我是智能客服助手，很高兴为您服务。请问有什么可以帮您？',
            time: dayjs().format('HH:mm')
        }
    ])
    const sending = ref(false)
    const deepSearch = ref(false)
    const historyDrawerVisible = ref(false)
    const historySearchKeyword = ref('')
    const historyMessages = ref([])

    // 过滤后的历史消息
    const filteredHistoryMessages = computed(() => {
        if (!historySearchKeyword.value) {
            return historyMessages.value
        }
        const keyword = historySearchKeyword.value.toLowerCase()
        return historyMessages.value.filter(chat =>
            chat.preview.toLowerCase().includes(keyword)
        )
    })

    // 保存当前对话到历史记录
    const saveToHistory = () => {
        if (messages.value.length <= 1) return // 只有欢迎消息时不保存
        const chat = {
            id: Date.now(),
            time: dayjs().format('YYYY-MM-DD HH:mm'),
            messages: [...messages.value],
            preview: messages.value[messages.value.length - 1].content
        }
        historyMessages.value.unshift(chat)
        try {
            localStorage.setItem('chatHistory', JSON.stringify(historyMessages.value))
        } catch (e) {
            ElMessage.error('历史记录保存失败')
        }
    }

    const loadHistoryChat = (chat) => {
        ElMessageBox.confirm(
            '加载此历史对话将清空当前对话，是否继续？',
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        ).then(() => {
            messages.value = [...chat.messages]
            historyDrawerVisible.value = false
            ElMessage.success('历史对话已加载')
        }).catch(() => {})
    }

    // 删除历史对话
    const deleteHistoryChat = (index) => {
        ElMessageBox.confirm(
            '确定要删除这条历史对话记录吗？',
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        ).then(() => {
            historyMessages.value.splice(index, 1)
            try {
                localStorage.setItem('chatHistory', JSON.stringify(historyMessages.value))
            } catch (e) {
                ElMessage.error('历史记录保存失败')
            }
            ElMessage.success('删除成功')
        }).catch(() => {})
    }

    // 显示历史对话抽屉
    const showHistoryDialog = () => {
        historyDrawerVisible.value = true
        // 从本地存储加载历史记录
        try {
            const savedHistory = localStorage.getItem('chatHistory')
            if (savedHistory) {
                historyMessages.value = JSON.parse(savedHistory)
            }
        } catch (e) {
            ElMessage.error('历史记录加载失败')
        }
    }

    // 发送消息
    const sendMessage = async (inputMessage, userAvatar, assistantAvatar, scrollToBottom, callback) => {
        if (!inputMessage.value.trim()) return
        const userMessage = {
            id: messageId++,
            type: 'user',
            content: inputMessage.value,
            time: dayjs().format('HH:mm')
        }
        messages.value.push(userMessage)
        const loadingMessage = {
            id: messageId++,
            type: 'assistant',
            content: '正在思考中...',
            time: dayjs().format('HH:mm'),
            isLoading: true
        }
        messages.value.push(loadingMessage)
        inputMessage.value = ''
        sending.value = true
        await scrollToBottom && scrollToBottom()
        try {
            // 这里应替换为你的后端接口
            // const response = await axios.post('/api/chat/spark', { message: userMessage.content, deepSearch: deepSearch.value })
            // 模拟回复
            await new Promise(r => setTimeout(r, 800))
            const response = { data: { content: '这是AI的回复内容。' } }
            const index = messages.value.findIndex(m => m.id === loadingMessage.id)
            messages.value.splice(index, 1, { ...loadingMessage, content: response.data.content, isLoading: false })
            saveToHistory()
            callback && callback()
        } catch (error) {
            const index = messages.value.findIndex(m => m.id === loadingMessage.id)
            messages.value.splice(index, 1, { ...loadingMessage, content: '回复失败，请稍后再试', isError: true })
            ElMessage.error('发送消息失败')
        } finally {
            sending.value = false
            await scrollToBottom && scrollToBottom()
        }
    }

    // 重试消息
    const retryMessage = async (message) => {
        try {
            message.content = '正在重新尝试...'
            message.isError = false
            // const response = await axios.post('/api/chat/spark', { message: messages.value[messages.value.indexOf(message)-1].content, deepSearch: deepSearch.value })
            await new Promise(r => setTimeout(r, 800))
            message.content = '这是AI的重试回复内容。'
        } catch {
            message.content = '重试失败，请稍后再试'
            message.isError = true
        }
    }

    // 切换深度搜索
    const toggleDeepSearch = () => {
        deepSearch.value = !deepSearch.value
        ElMessage.success(`已切换至${deepSearch.value ? '深度' : '普通'}模式`)
    }

    // 清空对话
    const clearChat = () => {
        messages.value = [{
            id: messageId++,
            type: 'assistant',
            content: '您好！我是智能客服助手，很高兴为您服务。请问有什么可以帮您？',
            time: dayjs().format('HH:mm')
        }]
    }

    // 过滤历史消息
    const filterHistoryMessages = () => {
        // 这里已由computed实现
    }

    return {
        messages,
        sending,
        deepSearch,
        historyDrawerVisible,
        historySearchKeyword,
        historyMessages,
        filteredHistoryMessages,
        saveToHistory,
        loadHistoryChat,
        deleteHistoryChat,
        showHistoryDialog,
        sendMessage,
        retryMessage,
        toggleDeepSearch,
        clearChat,
        filterHistoryMessages
    }
}