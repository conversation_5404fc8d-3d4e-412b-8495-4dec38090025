<script setup>
import { ref } from 'vue'
import { Document, Close } from '@element-plus/icons-vue'
const props = defineProps({ detailData: Object })
const showDescAll = ref(false)
const showRuleAll = ref(false)
const stageDetailDialogVisible = ref(false)
const stageDetailData = ref(null)
const showStageRemarkAll = ref(false)
function openStageDetail(stage) {
  stageDetailData.value = stage
  showStageRemarkAll.value = false
  stageDetailDialogVisible.value = true
}
function getLevelLabel(level) {
  return {
    national: '国家级',
    provincial: '省级',
    municipal: '市级',
    school: '校级',
    college: '院级'
  }[level] || level
}
function getParticipationTypeLabel(type) {
  return {
    individual: '个人赛',
    team: '团体赛'
  }[type] || type
}
function getStageLabel(type) {
  return {
    preliminary: '初赛',
    semifinal: '复赛',
    final: '决赛',
    qualification: '资格赛',
    elimination: '淘汰赛',
    round: '轮次赛'
  }[type] || type
}
function formatRange(start, end) {
  if (!start || !end) return '-'
  return `${formatDate(start)} ~ ${formatDate(end)}`
}
function formatDate(val) {
  if (!val) return ''
  if (typeof val === 'string') val = new Date(val)
  return val instanceof Date ? val.toLocaleString() : val
}
function getFormLabel(form) {
  return {
    online: '线上',
    offline: '线下'
  }[form] || form
}
</script>

<template>
  <el-descriptions :column="2" border>
    <el-descriptions-item label="赛事名称">{{ detailData.name }}</el-descriptions-item>
    <el-descriptions-item label="赛事编号">{{ detailData.code }}</el-descriptions-item>
    <el-descriptions-item label="赛事级别">{{ getLevelLabel(detailData.level) }}</el-descriptions-item>
    <el-descriptions-item label="参赛形式">{{ getParticipationTypeLabel(detailData.participationType || detailData.participation_type) }}</el-descriptions-item>
    <el-descriptions-item label="主办方">{{ detailData.organizer }}</el-descriptions-item>
    <el-descriptions-item label="赛事简介" :span="2">
      <span v-if="!showDescAll">
        {{ detailData.description?.slice(0, 100) }}
        <span v-if="detailData.description && detailData.description.length > 100">
          ... <a style="color:#409eff;cursor:pointer;" @click="showDescAll = true">展开</a>
        </span>
      </span>
      <span v-else>
        {{ detailData.description }} <a style="color:#409eff;cursor:pointer;" @click="showDescAll = false">收起</a>
      </span>
    </el-descriptions-item>
    <el-descriptions-item label="赛事规则" :span="2">
      <span v-if="!showRuleAll">
        {{ detailData.rules?.slice(0, 100) }}
        <span v-if="detailData.rules && detailData.rules.length > 100">
          ... <a style="color:#409eff;cursor:pointer;" @click="showRuleAll = true">展开</a>
        </span>
      </span>
      <span v-else>
        {{ detailData.rules }} <a style="color:#409eff;cursor:pointer;" @click="showRuleAll = false">收起</a>
      </span>
    </el-descriptions-item>
    <el-descriptions-item label="阶段" :span="2">
      <div v-if="detailData.stages && detailData.stages.length" style="display: flex; flex-direction: column; gap: 0; width: 100%;">
        <el-card
          v-for="(stage, idx) in detailData.stages"
          :key="idx"
          class="stage-card stage-card-flex minimal-stage-card"
          shadow="never"
          style="width: 100%; margin-bottom: 12px; padding: 18px 24px;"
        >
          <div class="stage-card-row">
            <div class="stage-card-left">
              <div class="stage-type-label">{{ getStageLabel(stage.stage_type) }}</div>
              <div class="stage-time-row">报名：{{ formatRange(stage.enroll_start_time, stage.enroll_end_time) }}</div>
              <div class="stage-time-row">提交：{{ formatRange(stage.submit_start_time, stage.submit_end_time) }}</div>
              <div class="stage-time-row">评审：{{ formatRange(stage.judge_start_time, stage.judge_end_time) }}</div>
            </div>
            <div class="stage-card-right">
              <div class="stage-leader-label">负责人</div>
              <div class="stage-leader-value">{{ stage.leader }}</div>
              <el-button
                size="small"
                type="primary"
                style="margin-top: 12px; width: 70px;"
                @click="openStageDetail(stage)"
              >详情</el-button>
            </div>
          </div>
        </el-card>
      </div>
      <div v-else>无</div>
    </el-descriptions-item>
    <el-descriptions-item label="官方文件" :span="2">
      <ul v-if="detailData.official_files && detailData.official_files.length">
        <li v-for="file in detailData.official_files" :key="file.uid">
          <el-icon><Document /></el-icon>
          <a :href="file.file_url" target="_blank">{{ file.file_name }}</a>
        </li>
      </ul>
      <span v-else>无</span>
    </el-descriptions-item>
  </el-descriptions>
  <el-dialog
    v-model="stageDetailDialogVisible"
    class="unified-dialog"
    title=""
    width="520px"
    append-to-body
    :modal="true"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <template #header>
      <div class="dialog-header">
        <el-icon class="header-icon"><Document /></el-icon>
        <span class="header-title">{{ getStageLabel(stageDetailData?.stage_type) }}阶段详情</span>
        <el-button class="header-close" type="text" @click="stageDetailDialogVisible = false">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </template>
    <transition name="fade-slide">
      <div v-if="stageDetailData" class="dialog-body-animate">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="阶段">{{ getStageLabel(stageDetailData.stage_type) }}</el-descriptions-item>
          <el-descriptions-item label="举办形式">
            <el-tag :type="stageDetailData.stage_form === 'online' ? 'success' : 'warning'" effect="plain">
              {{ getFormLabel(stageDetailData.stage_form) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="报名时间">
            {{ formatRange(stageDetailData.enroll_start_time, stageDetailData.enroll_end_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ formatRange(stageDetailData.submit_start_time, stageDetailData.submit_end_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="评审时间">
            {{ formatRange(stageDetailData.judge_start_time, stageDetailData.judge_end_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="负责人">{{ stageDetailData.leader }}</el-descriptions-item>
          <el-descriptions-item label="电话">{{ stageDetailData.leader_phone }}</el-descriptions-item>
          <el-descriptions-item label="地点">{{ stageDetailData.location || '-' }}</el-descriptions-item>
          <el-descriptions-item label="说明" :span="2">
            <span v-if="!showStageRemarkAll">
              {{ stageDetailData.remark?.slice(0, 80) }}
              <span v-if="stageDetailData.remark && stageDetailData.remark.length > 80">
                ... <a style="color:#409eff;cursor:pointer;" @click="showStageRemarkAll = true">展开</a>
              </span>
            </span>
            <span v-else>
              {{ stageDetailData.remark }} <a style="color:#409eff;cursor:pointer;" @click="showStageRemarkAll = false">收起</a>
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </transition>
    <template #footer>
      <el-button type="primary" @click="stageDetailDialogVisible = false" style="width: 120px;">关闭</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.minimal-stage-card {
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  background: #f8fafc !important;
  box-shadow: none !important;
}
.stage-card-flex {
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
}
.stage-card-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  height: 100%;
}
.stage-card-left {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
}
.stage-type-label {
  font-weight: bold;
  font-size: 16px;
  color: #6366f1;
  margin-bottom: 4px;
}
.stage-time-row {
  font-size: 13px;
  color: #666;
  margin-bottom: 2px;
}
.stage-card-right {
  flex: 0.9;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  gap: 2px;
  min-width: 90px;
}
.stage-leader-label {
  font-size: 13px;
  color: #888;
  margin-bottom: 2px;
}
.stage-leader-value {
  font-size: 14px;
  color: #222;
  font-weight: 500;
  margin-bottom: 4px;
}
.unified-dialog :deep(.el-dialog__header) {
  padding: 20px;
  margin: 0;
  border-bottom: 1px solid #ebeef5;
  background: linear-gradient(90deg, #f0f4ff 0%, #f8fafc 100%);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}
.unified-dialog :deep(.el-dialog__body) {
  max-height: 60vh;
  overflow-y: auto;
  padding: 30px 20px;
  background: #fff;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(80,120,200,0.08);
}
.unified-dialog :deep(.el-dialog__footer) {
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 2;
  border-top: 1px solid #ebeef5;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
}
.dialog-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: bold;
  color: #222;
  padding: 0;
}
.header-icon {
  font-size: 22px;
  color: #6366f1;
}
.header-title {
  flex: 1;
  font-size: 20px;
  font-weight: bold;
  color: #222;
}
.header-close {
  margin-left: auto;
  color: #888;
  font-size: 18px;
}
.dialog-body-animate {
  animation: fade-slide-in 0.3s cubic-bezier(.4,0,.2,1);
}
@keyframes fade-slide-in {
  0% { opacity: 0; transform: translateY(30px); }
  100% { opacity: 1; transform: translateY(0); }
}
</style>