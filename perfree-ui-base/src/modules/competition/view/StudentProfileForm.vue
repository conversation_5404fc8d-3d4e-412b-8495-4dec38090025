<template>
  <div class="page">
    <div class="search-box">
      <el-card class="profile-card">
        <div class="form-title">个人信息填写</div>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" class="profile-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="form.name" placeholder="请输入真实姓名" style="width: 100%" />
              </el-form-item>
              <el-form-item label="学校" prop="school">
                <el-select
                    v-model="form.school"
                    filterable
                    clearable
                    placeholder="请选择学校"
                    style="width: 100%"
                    @change="onSchoolChange"
                    @visible-change="handleSchoolDropdownVisible"
                >
                  <el-option
                      v-for="item in schoolOptions"
                      :key="item.school_id || item"
                      :label="item.name || item"
                      :value="item.school_id || item"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="院系" prop="department">
                <el-select
                    v-model="form.department"
                    placeholder="请选择院系"
                    :disabled="!departmentOptions.length"
                    filterable
                    clearable
                    style="width: 100%"
                    @change="onDepartmentChange"
                >
                  <el-option
                      v-for="item in departmentOptions"
                      :key="item"
                      :label="item"
                      :value="item"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="专业" prop="majorType">
                <el-select
                    v-model="form.majorType"
                    placeholder="请选择专业"
                    :disabled="!majorOptions.length"
                    filterable
                    clearable
                    style="width: 100%"
                >
                  <el-option
                      v-for="item in majorOptions"
                      :key="item.special_name"
                      :label="item.special_name"
                      :value="item.special_name"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="学号" prop="studentId">
                <el-input
                    v-model="form.studentId"
                    placeholder="请输入学号"
                    @input="handleStudentIdInput"
                    style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="身份证号" prop="idCard">
                <el-input v-model="form.idCard" placeholder="请输入身份证号" maxlength="18" show-word-limit style="width: 100%" />
              </el-form-item>
              <el-form-item label="入学年份" prop="enrollYear">
                <el-input v-model="form.enrollYear" placeholder="如：2023" style="width: 100%" />
              </el-form-item>
              <el-form-item label="学历" prop="education">
                <el-select v-model="form.education" placeholder="请选择学历" style="width: 100%">
                  <el-option label="本科" value="本科" />
                  <el-option label="专科" value="专科" />
                  <el-option label="专升本" value="专升本" />
                  <el-option label="硕士" value="硕士" />
                  <el-option label="博士" value="博士" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="年级" prop="grade">
                <el-select v-model="form.grade" placeholder="请选择年级" style="width: 100%">
                  <el-option label="一年级" value="一年级" />
                  <el-option label="二年级" value="二年级" />
                  <el-option label="三年级" value="三年级" />
                  <el-option label="四年级" value="四年级" />
                  <el-option label="五年级" value="五年级" />
                </el-select>
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" style="width: 100%" />
              </el-form-item>
              <el-form-item label="手机" prop="phone">
                <el-input
                    v-model="form.phone"
                    placeholder="请输入手机号"
                    @input="handlePhoneInput"
                    maxlength="11"
                    show-word-limit
                    style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="作者照片" prop="authorPhoto">
                <el-upload
                    class="avatar-uploader"
                    :action="uploadUrl"
                    :file-list="authorPhotoFileList"
                    :limit="1"
                    :before-upload="beforeAuthorPhotoUpload"
                    :on-success="handleAuthorPhotoSuccess"
                    :on-remove="handleAuthorPhotoRemove"
                    list-type="picture-card"
                    :data="{ studentId: form.studentId }"
                    :disabled="!form.studentId"
                    :show-file-list="false"
                >
                  <i class="el-icon-plus" v-if="!form.authorPhoto"></i>
                  <img v-else :src="form.authorPhoto" class="avatar" />
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="学生证照片" prop="studentCardPhoto">
                <el-upload
                    class="avatar-uploader"
                    :action="uploadUrl"
                    :file-list="studentCardPhotoFileList"
                    :limit="1"
                    :before-upload="beforeStudentCardPhotoUpload"
                    :on-success="handleStudentCardPhotoSuccess"
                    :on-remove="handleStudentCardPhotoRemove"
                    list-type="picture-card"
                    :data="{ studentId: form.studentId }"
                    :disabled="!form.studentId"
                    :show-file-list="false"
                >
                  <i class="el-icon-plus" v-if="!form.studentCardPhoto"></i>
                  <img v-else :src="form.studentCardPhoto" class="avatar" />
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 32px;">
            <el-col :span="12">
              <el-form-item>
                <el-button type="primary" :loading="loading" @click="submitForm" size="default">保存</el-button>
                <el-button @click="resetForm" size="default">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { submitStudentProfile, getStudentProfile, getSchoolList, getDepartmentList, getMajorList } from '../api/student'

const formRef = ref()
const loading = ref(false)
const authorPhotoFileList = ref([])
const studentCardPhotoFileList = ref([])

const form = ref({
  name: '',
  school: '',
  majorType: '',
  enrollYear: '',
  education: '',
  grade: '',
  email: '',
  phone: '',
  studentId: '',
  idCard: '',
  authorPhoto: '',
  studentCardPhoto: ''
})

const validateEnrollYear = (rule, value, callback) => {
  const currentYear = new Date().getFullYear()
  const year = parseInt(value)
  if (year < 2000 || year > currentYear) {
    callback(new Error(`入学年份必须在2000年至${currentYear}年之间`))
  } else {
    callback()
  }
}

const validatePhone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入手机号'))
  } else if (!/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('手机号格式不正确，请输入11位数字'))
  } else {
    callback()
  }
}

const validateIdCard = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入身份证号'))
  } else if (!/^(\d{15}|\d{17}[\dXx])$/.test(value)) {
    callback(new Error('身份证号格式不正确'))
  } else {
    callback()
  }
}

const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  school: [
    { required: true, message: '请选择学校', trigger: 'change' }
  ],
  majorType: [
    { required: true, message: '请选择专业类别', trigger: 'change' }
  ],
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { validator: validateIdCard, trigger: 'blur' }
  ],
  enrollYear: [
    { required: true, message: '请输入入学年份', trigger: 'blur' },
    { pattern: /^\d{4}$/, message: '请输入4位数字年份', trigger: 'blur' },
    { validator: validateEnrollYear, trigger: 'blur' }
  ],
  education: [
    { required: true, message: '请选择学历', trigger: 'change' }
  ],
  grade: [
    { required: true, message: '请选择年级', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  authorPhoto: [
    { required: true, message: '请上传作者照片', trigger: 'change' }
  ],
  studentCardPhoto: [
    { required: true, message: '请上传学生证照片', trigger: 'change' }
  ]
}

const uploadUrl = '/api/competition/student/upload_portrait'

const schoolOptions = ref([])
const majorTypeOptions = ref([])


const selectedSchoolId = ref('')
const departmentOptions = ref([])
const majorOptions = ref([])

const hasLoadedProfileAndOptions = ref(false)

function handleAuthorPhotoSuccess(response, file, fileListArr) {
  if (response.code === 200) {
    form.value.authorPhoto = (response.data || '').trim();
    authorPhotoFileList.value = [{ url: form.value.authorPhoto, name: file.name }];
    ElMessage.success('图片上传成功！');
  } else {
    ElMessage.error(response.msg ? '作者照片上传失败：' + response.msg : '作者照片上传失败');
    form.value.authorPhoto = '';
    authorPhotoFileList.value = [];
  }
}
function handleAuthorPhotoRemove() {
  form.value.authorPhoto = '';
  authorPhotoFileList.value = [];
}
function handleStudentCardPhotoSuccess(response, file, fileListArr) {
  if (response.code === 200) {
    form.value.studentCardPhoto = (response.data || '').trim();
    studentCardPhotoFileList.value = [{ url: form.value.studentCardPhoto, name: file.name }];
    ElMessage.success('图片上传成功！');
  } else {
    ElMessage.error(response.msg ? '学生证照片上传失败：' + response.msg : '学生证照片上传失败');
    form.value.studentCardPhoto = '';
    studentCardPhotoFileList.value = [];
  }
}
function handleStudentCardPhotoRemove() {
  form.value.studentCardPhoto = '';
  studentCardPhotoFileList.value = [];
}

function beforeAuthorPhotoUpload(file) {
  const studentId = form.value.studentId || sessionStorage.getItem('studentId')
  if (!studentId) {
    ElMessage.error('请先填写学号再上传图片');
    return false;
  }
  return true;
}
function beforeStudentCardPhotoUpload(file) {
  const studentId = form.value.studentId || sessionStorage.getItem('studentId')
  if (!studentId) {
    ElMessage.error('请先填写学号再上传图片');
    return false;
  }
  return true;
}

async function submitForm() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true


    const studentId = form.value.studentId || sessionStorage.getItem('studentId')
    if (!studentId) {
      ElMessage.error('未获取到学号，请重新登录')
      loading.value = false
      return
    }
    const res = await submitStudentProfile({ ...form.value, studentId })
    if (res.code === 200) {
      ElMessage({
        type: 'success',
        message: '保存成功',
        duration: 2000
      })

      loadStudentProfile()
    } else {
      ElMessage({
        type: 'error',
        message: res.msg || '保存失败',
        duration: 3000
      })
    }
  } catch (error) {
    loading.value = false
    if (error && error.errors) {
      const firstField = Object.keys(error.errors)[0]
      const firstMsg = error.errors[firstField][0]?.message
      ElMessage({
        type: 'error',
        message: firstMsg || '请检查表单填写是否正确',
        duration: 3000
      })
    } else {
      ElMessage({
        type: 'error',
        message: '请检查表单填写是否正确',
        duration: 3000
      })
    }
  } finally {
    loading.value = false
  }
}

function resetForm() {
  ElMessageBox.confirm('确定要重置所有已填写的信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    form.value = {
      name: '',
      school: '',
      majorType: [],
      enrollYear: '',
      education: '',
      grade: '',
      email: '',
      phone: '',
      studentId: '',
      idCard: '',
      authorPhoto: '',
      studentCardPhoto: ''
    }
    if (formRef.value) {
      formRef.value.clearValidate()
    }
    ElMessage({
      type: 'success',
      message: '表单已重置',
      duration: 2000
    })
  }).catch(() => {

  })
}

const handlePhoneInput = (value) => {
  if (value.length > 11) {
    form.value.phone = value.slice(0, 11)
  }
}

const handleStudentIdInput = (value) => {
  if (value.length > 13) {
    form.value.studentId = value.slice(0, 13)
  }
}

async function loadStudentProfile() {
  try {
    const studentId = sessionStorage.getItem('studentId')
    if (!studentId) {

      return
    }
    ElMessage.info('正在拉取个人信息...')
    const res = await getStudentProfile(studentId)
    if (res.code === 200 && res.data) {
      const profileData = { ...res.data }
      if (Array.isArray(profileData.majorType)) {
        profileData.majorType = profileData.majorType.join(', ')
      } else if (typeof profileData.majorType !== 'string') {
        profileData.majorType = ''
      }
      form.value = { ...form.value, ...profileData }
    } else if (res.code !== 200) {
      ElMessage.error(res.msg || '加载个人信息失败')
    }
  } catch (err) {
    ElMessage.error('加载个人信息失败，请稍后重试')
  }
}

async function loadOptions() {
  try {
    const schoolRes = await getSchoolList();
    if (schoolRes.code === 200 && schoolRes.data) {
      schoolOptions.value = Array.isArray(schoolRes.data) ? schoolRes.data : Object.values(schoolRes.data);
    } else {
      ElMessage.error(schoolRes.msg || '加载学校列表失败');
      return;
    }
  } catch (err) {
    ElMessage.error('加载下拉选项失败，请稍后重试')
  }
}


async function onSchoolChange(schoolId) {
  form.value.department = '';
  form.value.majorType = '';
  departmentOptions.value = [];
  majorOptions.value = [];
  selectedSchoolId.value = schoolId;
  // 新增校验
  if (!schoolId) {
    ElMessage.error('请选择学校');
    return;
  }
  try {
    const res = await getDepartmentList(schoolId);
    if (res.code === 200) {
      departmentOptions.value = res.data || [];
    } else {
      ElMessage.error(res.msg || '加载院系列表失败');
    }
  } catch (error) {
    ElMessage.error('加载院系列表失败，请稍后重试');
  }
}
async function onDepartmentChange(departmentName) {
  form.value.majorType = '';
  majorOptions.value = [];
  // 新增校验
  if (!selectedSchoolId.value) {
    ElMessage.error('请先选择学校');
    return;
  }
  if (!departmentName) {
    ElMessage.error('请选择院系');
    return;
  }
  try {
    const res = await getMajorList(selectedSchoolId.value, departmentName);
    if (res.code === 200) {
      majorOptions.value = res.data || [];
    } else {
      ElMessage.error(res.msg || '加载专业列表失败');
    }
  } catch (error) {
    ElMessage.error('加载专业列表失败，请稍后重试');
  }
}

function handleSchoolDropdownVisible(visible) {

}

onMounted(() => {
  loadOptions();
  // const studentId = sessionStorage.getItem('studentId');
  // if (studentId) {
  //   ElMessage.info('正在拉取个人信息...');
  //   loadStudentProfile();
  // }
})
</script>

<style scoped>
.page {
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

.search-box {
  margin-bottom: 20px;
}

.profile-card {
  background: transparent;
  border-radius: 4px;
  box-shadow: none;
  padding: 24px 20px 12px 20px;
  width: 100%;
  margin: 0;
  min-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
}

:deep(.el-card) {
  border: none;
  box-shadow: none;
}

:deep(.el-card__body) {
  padding: 0;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  letter-spacing: 1px;
}

.profile-form {
  margin-top: 0;
  background: transparent;
  padding: 24px;
  border-radius: 4px;
  box-shadow: none;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.image-preview-container {
  position: relative;
  width: 120px;
  height: 120px;
}

.image-preview-container:hover .image-actions {
  opacity: 1;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.image-actions .el-icon {
  font-size: 24px;
}

.upload-tip {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}


.avatar-uploader {
  width: 120px;
  height: 120px;
  display: inline-block;
  position: relative;
}
.avatar-uploader .avatar {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  border-radius: 8px;
}
.el-upload--picture-card {
  width: 120px !important;
  height: 120px !important;
  line-height: 120px !important;
}
.el-upload-list--picture-card .el-upload-list__item {
  width: 120px !important;
  height: 120px !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper:focus-within),
:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: #66b1ff;
  --el-button-hover-border-color: #66b1ff;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #409eff inset;
}
</style>
