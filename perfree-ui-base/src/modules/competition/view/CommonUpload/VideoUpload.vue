<template>
  <el-upload
      class="video-upload"
      action="#"
      accept=".mp4"
      drag
      multiple
      :auto-upload="false"
      :show-file-list="false"
      v-model:file-list="fileList"
      :on-change="handleChange"
      :on-remove="handleRemove"
  >
    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
    <div class="el-upload__text">
      拖拽视频文件到这里或者<em>点击上传</em>
    </div>
    <div class="el-upload__tip">
      支持MP4格式，建议时长不超过5分钟
    </div>
  </el-upload>
  <div v-if="tableData.length > 0" class="video-list">
    <div v-for="file in tableData" :key="file.uid" class="video-item">
      <div class="video-info">
        <el-icon class="video-icon"><VideoPlay /></el-icon>
        <span class="video-name">{{ file.name }}</span>
      </div>
      <div class="video-progress">
        <el-progress 
          :text-inside="true" 
          :stroke-width="8" 
          :percentage="file.percentage || 0"
          :status="file.percentage === 100 ? 'success' : ''"
        />
        <span v-if="typeof file.percentage === 'number'" class="progress-percent">{{ file.percentage }}%</span>
      </div>
      <div class="video-actions">
        <el-button 
          v-if="file.url" 
          type="primary" 
          size="small" 
          @click="handlePreviewUploadFileList(file)"
          class="preview-btn"
        >
          预览
        </el-button>
        <el-button 
          type="danger" 
          size="small" 
          @click="handleRemoveUploadFileList(file)"
          class="delete-btn"
        >
          删除
        </el-button>
      </div>
    </div>
  </div>
  <el-dialog 
    v-model="dialogTableVisible" 
    title="视频预览" 
    @close="handleClose"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
  >
    <div class="video-preview-container">
      <video 
        v-if="dialogTableVisible && videoPath" 
        width="100%" 
        height="auto" 
        controls 
        preload="metadata"
        id="video-play"
        class="preview-video"
      >
        <source :src="videoPath" type="video/mp4">
        <source :src="videoPath" type="video/webm">
        <source :src="videoPath" type="video/ogg">
        您的浏览器不支持 video 标签。
      </video>
      <div v-else-if="dialogTableVisible" class="video-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>视频加载中...</span>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import axios from 'axios'
import { UploadFilled, VideoPlay, Loading } from '@element-plus/icons-vue'
import { type UploadProps, UploadFile, UploadFiles, ElMessage } from 'element-plus'
import { ref, defineProps, defineEmits, watch, nextTick } from 'vue'

const props = defineProps<{
  fileList?: UploadFiles
}>()

const emit = defineEmits<{
  'update:fileList': [value: UploadFiles]
}>()

const fileList = ref<UploadFiles>(props.fileList || [])
const tableData = ref<UploadFiles>([])
const chunkSize = 1024 * 1024 * 8 // 8MB
const dialogTableVisible = ref(false)
const videoPath = ref('')


watch(() => props.fileList, (newVal) => {
  if (newVal) {
    fileList.value = newVal
  }
}, { deep: true })


watch(fileList, (newVal) => {
  emit('update:fileList', newVal)
}, { deep: true })


const uploadFileToServer = async (file: Blob, chunkNumber: number, chunkTotal: number, fileName: string) => {
  const form = new FormData()
  form.append("file", file)
  form.append("chunkNumber", chunkNumber.toString())
  form.append("chunkTotal", chunkTotal.toString())
  form.append("fileName", fileName)
  try {
    const result = await axios.post("/api/upload/video/chunk", form, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return result
  } catch (e) {
    console.error('分片上传失败', e)
    throw new Error('文件上传失败，请检查网络连接或联系管理员')
  }
}

const mergeFiles = async (chunkTotal: number, fileName: string) => {
  try {
    const result = await axios.get(`/api/upload/video/merge`, {
      params: { chunkTotal, fileName }
    })
    return result.data
  } catch (e) {
    console.error('合并文件失败', e)
    throw new Error('文件合并失败，请重试')
  }
}

const deleteFileByFileName = async (fileName: string) => {
  try {
    await axios.delete(`/api/upload/video/delete`, {
      params: { fileName }
    })
  } catch (e) {
    console.error('删除文件失败', e)
    // 删除失败不影响用户体验，只记录日志
  }
}

const handleChange: UploadProps['onChange'] = async (uploadFile, uploadFiles) => {
  try {
    tableData.value.push({ ...uploadFile })
    const index = tableData.value.findIndex(item => item.uid === uploadFile.uid)
    const fileName = uploadFile.name
    const fileSize = uploadFile.size || 0
    
    // 文件大小检查
    const maxSize = 500 * 1024 * 1024 // 500MB
    if (fileSize > maxSize) {
      throw new Error('文件大小不能超过500MB')
    }
    
    // 初始化进度
    uploadFile.percentage = 0
    tableData.value[index].percentage = 0
    
    // 分片上传
    const chunkTotals = Math.ceil(fileSize / chunkSize)
    for (let chunkNumber = 0, start = 0; chunkNumber < chunkTotals; chunkNumber++, start += chunkSize) {
      const end = Math.min(fileSize, start + chunkSize)
      const chunk = uploadFile.raw?.slice(start, end)
      if (!chunk) {
        throw new Error('文件读取失败')
      }
      
      const result = await uploadFileToServer(chunk, chunkNumber + 1, chunkTotals, fileName)
      // 计算进度百分比
      const chunkProgress = ((chunkNumber + 1) / chunkTotals) * 100
      uploadFile.percentage = Math.round(chunkProgress)
      tableData.value[index].percentage = Math.round(chunkProgress)
    }
    
    // 合并文件
    const videoUrl = await mergeFiles(chunkTotals, fileName)
    tableData.value[index].url = videoUrl
    tableData.value[index].percentage = 100
    uploadFile.percentage = 100
    uploadFile.url = videoUrl
    
    // 更新fileList
    fileList.value = [...uploadFiles]
  } catch (error) {
    console.error('上传失败:', error)
    // 从列表中移除失败的文件
    const index = tableData.value.findIndex(item => item.uid === uploadFile.uid)
    if (index !== -1) {
      tableData.value.splice(index, 1)
    }
    // 显示错误信息
    ElMessage.error(error.message || '上传失败，请重试')
  }
}



const handleRemove: UploadProps['onRemove'] = async (uploadFile, uploadFiles) => {
  const index = tableData.value.findIndex(item => item.uid === uploadFile.uid)
  if (index !== -1) tableData.value.splice(index, 1)
  await deleteFileByFileName(uploadFile.name)

  fileList.value = [...uploadFiles]
  ElMessage.success('删除成功')
}

const handleRemoveUploadFileList = async (file: UploadFile) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index !== -1) fileList.value.splice(index, 1)
  const index2 = tableData.value.findIndex(item2 => item2.uid === file.uid)
  if (index2 !== -1) tableData.value.splice(index2, 1)
  await deleteFileByFileName(file.name)

  emit('update:fileList', [...fileList.value])
  ElMessage.success('删除成功')
}

const handlePreviewUploadFileList = (row: UploadFile) => {
  if (!row.url) {
    ElMessage.warning('视频文件还未上传完成，无法预览')
    return
  }
  // url调试输出
  console.log('预览视频url:', row.url)
  // 自动补全url为绝对路径（如有需要）
  let url = row.url
  if (url && !/^https?:\/\//.test(url) && !url.startsWith('/')) {
    url = '/uploads/' + url
  }
  dialogTableVisible.value = true
  videoPath.value = url
  
  // 延迟加载视频，确保对话框已打开
  nextTick(() => {
    const videoElement = document.getElementById('video-play') as HTMLVideoElement
    if (videoElement) {
      videoElement.load()
      videoElement.onerror = () => {
        ElMessage.error('视频加载失败，请检查文件是否完整')
      }
    }
  })
}

const handleClose = () => {
  const myVideo = document.getElementById('video-play') as HTMLVideoElement
  if (myVideo) {
    myVideo.pause()
    myVideo.src = ''
    myVideo.load()
  }
  dialogTableVisible.value = false
  videoPath.value = ''
}
</script>

<style scoped>
.video-upload {
  margin-bottom: 20px;
}

.video-upload .el-upload {
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
  padding: 20px;
  text-align: center;
}

.video-upload .el-upload:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.video-upload .el-icon--upload {
  color: #909399;
  font-size: 28px;
  margin-bottom: 8px;
}

.video-upload .el-upload__text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.video-upload .el-upload__text em {
  color: #409eff;
  font-style: normal;
  font-weight: 500;
}

.video-upload .el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

/* 视频列表样式 */
.video-list {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.video-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: #fff;
  border: 1.5px solid #e4e7ed;
  border-radius: 10px;
  margin-bottom: 10px;
  transition: box-shadow 0.2s, border-color 0.2s, background 0.2s;
  min-width: 420px;
  max-width: 600px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.04);
}
.video-item:hover {
  border-color: #409eff;
  background: #f6faff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.10);
}
.video-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}
.video-icon {
  color: #409eff;
  font-size: 18px;
  margin-right: 8px;
  flex-shrink: 0;
}
.video-name {
  color: #303133;
  font-size: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
}
.video-progress {
  width: 120px;
  margin: 0 10px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  position: relative;
}
.progress-percent {
  margin-left: 10px;
  color: #409eff;
  font-weight: bold;
  font-size: 14px;
  min-width: 38px;
  text-align: left;
  letter-spacing: 1px;
}
.video-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}
.preview-btn {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  padding: 4px 10px;
  min-width: 48px;
}
.preview-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}
.delete-btn {
  background: #f56c6c;
  border-color: #f56c6c;
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  padding: 4px 10px;
  min-width: 48px;
  font-weight: 500;
}
.delete-btn:hover {
  background: #f78989;
  border-color: #f78989;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  border-radius: 4px;
  background: #f0f0f0;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
  background: #409eff;
}

:deep(.el-progress__text) {
  color: #606266;
  font-size: 12px;
  font-weight: normal;
}

/* 预览对话框样式 */
.el-dialog {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.video-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
}

.preview-video {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  max-width: 100%;
  max-height: 70vh;
}

.video-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #909399;
  font-size: 14px;
}

.video-loading .el-icon {
  font-size: 24px;
  color: #409eff;
}
</style>