<template>
  <div class="pro-upload-file-box">
    <el-upload
        ref="uploadRef"
        v-bind="uploadProps"
        :on-success="onSuccess"
        :before-upload="onBeforeUpload"
        :on-exceed="onExceed"
        :on-error="onError"
        :on-remove="onRemove"
        :file-list="fileList"
        :disabled="disabled"
        :auto-upload="true"
        :show-file-list="false"
        class="pro-upload-file"
        :class="{ 'is-disabled': disabled }"
        :multiple="allowFolder || multiple"
    >
      <slot>
        <el-button v-if="!drag" type="primary" :disabled="disabled">
          <el-icon class="upload-icon"><Plus /></el-icon>
          {{ buttonText }}
        </el-button>
        <div v-else class="upload-drag-content">
          <el-image
              class="img-upload"
              src="https://static.wxb.com.cn/frontEnd/images/ideacome-vue3-component/upload.png"
          />
          <div class="upload-guide">{{ dragTitle }}</div>
          <div v-if="accept" class="upload-support">
            支持扩展名：{{ accept.split(',').join(' ') }}
          </div>
        </div>
      </slot>
      <template #file>
        <slot name="file" />
      </template>
      <template #tip>
        <div v-if="tip" class="upload-tip">{{ tip }}</div>
      </template>
    </el-upload>

    <div v-if="showFileList" class="pro-upload-file-content">
      <div
          v-for="(file, index) in fileList"
          :key="file.uid"
          class="file-list-item"
          @mouseenter="hoverItem = file.uid"
          @mouseleave="hoverItem = ''"
          @click="onFileClick(file)"
      >
        <div class="content">
          <div class="file-list-item-left">
            <template v-if="isImageFile(file)">
              <el-image
                  class="file-icon"
                  :src="file.url || file.preview || ''"
                  fit="cover"
                  :preview-src-list="[file.url || file.preview || '']"
                  preview-teleported
              />
            </template>
            <template v-else>
              <el-image
                  v-if="fileTypeObj(file.name).showFileIcon"
                  class="file-icon"
                  :src="fileTypeObj(file.name).fileIconUrl"
              />
            </template>
            <div>
              <div class="file-list-item-name">{{ file.name }}</div>
              <div class="file-list-item-state">
                {{ file.loadingState ? "上传中" : "上传完成" }}
              </div>
            </div>
          </div>
          <template v-if="!file.loadingState">
            <el-icon
                v-if="hoverItem === file.uid && !disabled"
                class="file-list-item-icon-del"
                @click.stop="removeFile(file, index)"
            >
              <Close />
            </el-icon>
            <el-icon v-else class="file-list-item-icon">
              <Check />
            </el-icon>
          </template>
        </div>
        <el-progress
            v-if="file.percentage < 100"
            color="rgba(11, 211, 211, 1)"
            :stroke-width="2"
            :show-text="false"
            :percentage="file.percentage"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="ProUploadFile">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { Close, Check, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface ProFile {
  name: string;
  url: string;
  percentage: number;
  loadingState: number;
  type: string;
  uid: any;
  raw?: File;
  preview?: string;
  interval?: ReturnType<typeof setInterval>;
}

const props = defineProps({
  action: { type: String, required: true },
  accept: { type: String, default: '.xls,.xlsx' },
  maxSize: { type: Number, default: 0 },
  sizeUnit: { type: String, default: 'MB', validator: (v: string) => ['KB', 'MB'].includes(v) },
  drag: { type: Boolean, default: true },
  limit: { type: Number, default: 0 },
  multiple: { type: Boolean, default: false },
  showFileList: { type: Boolean, default: false },
  headers: { type: Object, default: () => ({}) },
  disabled: { type: Boolean, default: false },
  tip: { type: String, default: '' },
  dragTitle: { type: String, default: '点击或将文件拖拽到这里上传' },
  buttonText: { type: String, default: '文件上传' },
  modelValue: { type: Array, default: () => [] },
  allowFolder: { type: Boolean, default: false },
  data: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['update:modelValue', 'success', 'fileClick', 'remove', 'error', 'exceed'])

const uploadRef = ref()
const hoverItem = ref('')
const fileList = ref<ProFile[]>([...props.modelValue as ProFile[]])

watch(
    () => props.modelValue,
    (val) => {
      fileList.value = [...val as ProFile[]]
    }
)
watch(
    fileList,
    (val) => {
      emit('update:modelValue', val)
    },
    { deep: true }
)

const fileTypeList = computed(() => {
  if (!props.accept) return []
  return props.accept.split(',').map(item => item.replace('.', '').toLowerCase())
})
const maxSizeInKB = computed(() => {
  if (!props.maxSize) return 0
  return props.sizeUnit === 'MB' ? props.maxSize * 1024 : props.maxSize
})

const uploadProps = computed(() => ({
  action: props.action,
  accept: props.accept,
  limit: props.limit,
  drag: props.drag,
  multiple: props.multiple,
  headers: props.headers,
  disabled: props.disabled,
  data: props.data
}))

const FILE_TYPE_MAP = {
  pdf: ['pdf'],
  excel: ['xls', 'xlsx', 'et'],
  csv: ['csv'],
  image: ['png', 'jpg', 'jpeg', 'gif', 'bmp'],
  doc: ['doc', 'docx'],
  zip: ['zip']
}
const ICON_MAP = {
  pdf: 'https://static.wxb.com.cn/frontEnd/images/ideacome-vue3-component/pdf.png',
  excel: 'https://static.wxb.com.cn/frontEnd/images/ideacome-vue3-component/exc.png',
  csv: 'https://static.wxb.com.cn/frontEnd/images/ideacome-vue3-component/csv.png',
  image: 'https://static.wxb.com.cn/frontEnd/images/ideacome-vue3-component/image.png',
  doc: 'https://static.wxb.com.cn/frontEnd/images/ideacome-vue3-component/doc.png',
  zip: 'https://static.wxb.com.cn/frontEnd/images/ideacome-vue3-component/zip.png'
}
function isFileType(fileName: string, type: string) {
  const ext = fileName.split('.').pop()?.toLowerCase()
  return FILE_TYPE_MAP[type]?.includes(ext) || false
}
function fileTypeObj(fileName: string) {
  for (const [type, url] of Object.entries(ICON_MAP)) {
    if (isFileType(fileName, type)) {
      return { showFileIcon: true, fileIconUrl: url }
    }
  }
  return { showFileIcon: false, fileIconUrl: '' }
}

function validateFileType(file: File) {
  if (!props.accept) return true
  const ext = file.name.split('.').pop()?.toLowerCase()
  if (!fileTypeList.value.includes(ext!)) {
    ElMessage.warning(`仅支持 ${fileTypeList.value.join('、')} 格式`)
    return false
  }
  return true
}
function validateFileSize(file: File) {
  if (!props.maxSize) return true
  const fileSize = file.size / 1024
  if (fileSize > maxSizeInKB.value) {
    ElMessage.warning(`大小不能超过 ${props.maxSize}${props.sizeUnit}！`)
    return false
  }
  return true
}
function createFileObject(file: any): ProFile {
  return {
    name: file.name,
    url: file.url || '',
    percentage: 0,
    loadingState: 1,
    type: file.type,
    uid: file.uid,
    raw: file.raw,
  }
}
function isImageFile(file: any) {
  const ext = file.name?.split('.').pop()?.toLowerCase()
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)
}
function addFileToList(file: any) {
  const obj = createFileObject(file)
  if (isImageFile(obj) && file.raw) {
    obj.preview = URL.createObjectURL(file.raw)
  }
  obj.interval = setInterval(() => {
    const idx = fileList.value.findIndex((i) => i.uid === obj.uid)
    const target = fileList.value[idx]
    if (target && target.percentage < 90) target.percentage += 10
  }, 300)
  fileList.value.push(obj)
}
function removeFile(file: any, index: number) {
  fileList.value.splice(index, 1)
  uploadRef.value?.handleRemove(file)
  emit('remove', file, fileList.value)
}
function onBeforeUpload(file: File) {
  if (!validateFileType(file)) return false
  if (!validateFileSize(file)) return false
  addFileToList(file)
  return true
}
function onSuccess(response: any, uploadFile: any, uploadFiles: any[]) {
  const idx = fileList.value.findIndex((item) => item.uid === uploadFile.uid)
  if (idx === -1) {
    emit('success', response, uploadFile, uploadFiles)
    return;
  }
  const target = fileList.value[idx]
  if (response.code === 200) {
    if (typeof response.data === 'string') {
      target.url = response.data
    } else if (response.data && response.data.fileUrl) {
      target.url = response.data.fileUrl
    }
    if (response.data && response.data.fileName) {
      target.name = response.data.fileName
    }
    target.percentage = 100
    target.loadingState = 0
  } else {
    fileList.value.splice(idx, 1)
    uploadRef.value?.handleRemove(uploadFile)
    ElMessage.error(response?.msg || response?.message || '上传失败')
  }
  target.interval && clearInterval(target.interval)
  emit('success', response, uploadFile, uploadFiles)
}
function onError(error: any, uploadFile: any, uploadFiles: any[]) {
  const idx = fileList.value.findIndex((item) => item.uid === uploadFile.uid)
  if(idx > -1) {
    uploadRef.value?.handleRemove(uploadFile)
    fileList.value[idx]?.interval && clearInterval(fileList.value[idx].interval)
    fileList.value.splice(idx, 1)
  }
  ElMessage.error('上传失败')
  emit('error', error, uploadFile, uploadFiles)
}
function onExceed(files: File[], uploadFiles: File[]) {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
  emit('exceed', files, uploadFiles)
}
function onRemove(file: any, newFileList: any[]) {
  emit('remove', file, newFileList)
}
function onFileClick(file: any) {
  if (file.loadingState === 1) {
    ElMessage.warning('文件上传中！')
    return
  }
  emit('fileClick', file)
}

onMounted(() => {
  nextTick(() => {
    if (props.allowFolder && uploadRef.value) {
      const input = uploadRef.value.$el.querySelector('input[type="file"]')
      if (input) {
        input.setAttribute('webkitdirectory', 'true')
        input.setAttribute('directory', 'true')
        input.setAttribute('multiple', 'true')
      }
    }
  })
})
</script>

<style lang="less" scoped>
.pro-upload-file-box {
  text-align: left;
  .is-disabled {
    :deep(.el-upload-dragger) {
      cursor: not-allowed;
    }
  }
  .pro-upload-file {
    position: relative;
    .upload-icon {
      font-size: 16px;
      margin-right: 4px;
    }
    .img-upload {
      width: 48px;
      height: 48px;
    }
    .upload-guide {
      color: rgba(0, 5, 27, 0.85);
      font-size: 14px;
    }
    .upload-support {
      color: rgba(0, 5, 27, 0.45);
      font-size: 12px;
      margin-top: 4px;
    }
  }
  .upload-tip {
    margin-top: 8px;
    line-height: 20px;
    text-align: left;
    font-size: 12px;
    color: #909399;
  }
  .pro-upload-file-content {
    width: 100%;
    margin-top: 16px;
    .file-list-item {
      width: 100%;
      cursor: pointer;
      margin-bottom: 8px;
      background: #f6f7fb;
      border-radius: 4px;
      .content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        background: #f6f7fb;
        padding: 12px 20px;
        box-sizing: border-box;
        border-radius: 4px;
      }
    }
    .file-list-item-left {
      width: calc(100% - 32px);
      display: flex;
      align-items: center;
      .file-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
        flex-shrink: 0;
      }
    }
    .file-list-item-name {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: rgba(0, 5, 27, 0.85);
      font-size: 14px;
    }
    .file-list-item-state {
      font-size: 12px;
      color: rgba(0, 5, 27, 0.45);
    }
    .file-list-item-icon {
      font-size: 16px;
      color: #0bd3d3;
    }
    .file-list-item-icon-del {
      color: rgba(0, 5, 27, 0.65);
      font-size: 16px;
    }
  }
  :deep(.el-upload-dragger) {
    border: 1px dashed rgba(11, 211, 211, 1);
    background: rgba(11, 211, 211, 0.02);
    &.is-dragover,
    &:hover {
      background: rgba(11, 211, 211, 0.1) !important;
      border: 1px dashed rgba(11, 211, 211, 1) !important;
    }
  }
  :deep(.el-progress-bar__outer) {
    border-radius: 4px;
  }
}
</style>