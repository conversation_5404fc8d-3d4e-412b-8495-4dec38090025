<template>
  <div class="teacher-profile-form-left">
    <div class="form-title">教师信息填写</div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" class="profile-form">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="工号" prop="work_id">
        <el-input v-model="form.work_id" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" maxlength="11" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" />
      </el-form-item>
      <el-form-item label="学历" prop="education">
        <el-select v-model="form.education">
          <el-option label="本科" value="本科" />
          <el-option label="专科" value="专科" />
          <el-option label="专升本" value="专升本" />
          <el-option label="硕士" value="硕士" />
          <el-option label="博士" value="博士" />
        </el-select>
      </el-form-item>
      <el-form-item label="入职年份" prop="entry_year">
        <el-input v-model="form.entry_year" maxlength="4" />
      </el-form-item>
      <el-form-item label="职称" prop="position">
        <el-input v-model="form.position" />
      </el-form-item>
      <el-form-item label="任职学校" prop="department">
        <el-select v-model="form.department" filterable clearable>
          <el-option v-for="item in schoolOptions" :key="item.school_id" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="工作证照片" prop="work_photo">
        <el-upload
            :action="uploadUrl"
            :data="getUploadData"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :show-file-list="false"
            list-type="picture-card"
        >
          <img v-if="form.work_photo" :src="form.work_photo" class="avatar" />
          <i v-else class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import SparkMD5 from 'spark-md5'
import { v4 as uuidv4 } from 'uuid'
import { getSchoolList } from '../api/student'
import { submitTeacherProfile } from '../api/Teacher'

const formRef = ref()
const form = ref({
  name: '',
  work_id: '',
  phone: '',
  email: '',
  education: '',
  entry_year: '',
  position: '',
  department: '',
  work_photo: ''
})
const schoolOptions = ref([])
const uploadUrl = '/api/competition/teacher/upload_work_photo'


const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度2-20', trigger: 'blur' }
  ],
  work_id: [
    { required: true, message: '请输入工号', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  education: [
    { required: true, message: '请选择学历', trigger: 'change' }
  ],
  entry_year: [
    { required: true, message: '请输入入职年份', trigger: 'blur' },
    { pattern: /^\d{4}$/, message: '年份格式不正确', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请输入职称', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择任职学校', trigger: 'change' }
  ],
  work_photo: [
    { required: true, message: '请上传工作证照片', trigger: 'change' }
  ]
}

// 获取学校列表
onMounted(() => {
  loadSchoolOptions()
})

async function loadSchoolOptions() {
  try {
    const res = await getSchoolList()
    if (res.code === 200) {
      if (Array.isArray(res.data)) {
        schoolOptions.value = res.data
      } else if (typeof res.data === 'object') {
        schoolOptions.value = Object.values(res.data)
      } else {
        ElMessage.error('学校数据格式异常')
        return
      }
    } else {
      ElMessage.error(res.msg || '加载学校列表失败')
    }
  } catch (err) {
    ElMessage.error('加载学校列表失败')
  }
}


const beforeUpload = (file) => {
  if (!form.value.work_id) {
    ElMessage.error('请先填写工号')
    return false
  }
  return true
}


const getUploadData = (file) => {
  const userId = getUserId()
  return {
    userId,
    name: file.name,
    chunks: 1,
    chunk: 0,
    chunkSize: file.size,
    md5: '',
    uid: uuidv4().replace(/-/g, '').toLowerCase()
  }
}


const handleUploadSuccess = (response, file) => {
  if (response.code === 200) {
    const userId = getUserId()
    form.value.work_photo = `/api/competition/teacher/getTWP/${userId}`
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}


const handleUploadError = () => {
  ElMessage.error('图片上传失败')
}


function getUserId() {
  const tokenInfo = JSON.parse(localStorage.getItem('token_info') || '{}')
  return tokenInfo.userId || 1
}


const submitForm = async () => {
  await formRef.value.validate()
  const userId = getUserId()
  const data = { ...form.value, userId }
  const res = await submitTeacherProfile(data)
  console.log('submitTeacherProfile 返回：', res)
  const raw = res.data || res
  const result = raw.data || raw
  console.log('raw:', raw)
  console.log('result:', result)
  if ((raw.code === 200 || raw.success) && result.success) {
    ElMessage.success('保存成功')
  } else {
    ElMessage.error(raw.msg || '保存失败')
  }
}


const resetForm = () => {
  form.value = {
    name: '',
    work_id: '',
    phone: '',
    email: '',
    education: '',
    entry_year: '',
    position: '',
    department: '',
    work_photo: ''
  }
  if (formRef.value) formRef.value.clearValidate()
}
</script>

<style scoped>
.teacher-profile-form-left {
  min-height: 100vh;
  width: 100vw;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 40px 0 0 80px;
}
.form-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  letter-spacing: 1px;
}
.profile-form {
  width: 500px;
  background: transparent;
}
.avatar {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
}
</style>
