<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  ArrowDown,
  Trophy,
  Document,
  Edit,
  View,
  Delete,
  Upload,
  Download,
  Share,
  User,
  Link
} from '@element-plus/icons-vue'
import { 
  workDetailsApi, 
  workDataUtils, 
  statusMap, 
  typeMap, 
  roleMap 
} from '../api/WorkDetails.js'

const router = useRouter()


const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const selectedWork = ref(null)


const participantsDialogVisible = ref(false)
const workInfoDialogVisible = ref(false)
const instructorDialogVisible = ref(false)


const worksData = ref([])



const participantsRules = {
  'authors.*.name': [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  'authors.*.studentId': [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ]
}

const workInfoRules = {
  name: [
    { required: true, message: '请输入作品名称', trigger: 'blur' }
  ],
  competitionType: [
    { required: true, message: '请选择赛事类型', trigger: 'change' }
  ],
  abstract: [
    { required: true, message: '请输入作品简介', trigger: 'blur' }
  ],
  videoLink: [
    { required: true, message: '请输入网盘链接', trigger: 'blur' }
  ],
  videoCode: [
    { required: true, message: '请输入提取码', trigger: 'blur' }
  ]
}

const instructorRules = {
  'instructors.*.name': [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  'instructors.*.role': [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}


const participantsFormRef = ref()
const workInfoFormRef = ref()
const instructorFormRef = ref()

const participantsForm = reactive({
  authors: []
})

const workInfoForm = reactive({
  name: '',
  type: '', // 修正字段名
  abstract: '',
  projectPlan: null,
  videoLink: '',
  videoCode: ''
})

const instructorForm = reactive({
  instructors: []
})


const fileList = ref([])


const handleFileChange = (file) => {
  const isLt50M = file.raw.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过50MB!')
    return false
  }
  

  const allowedTypes = ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.zip', '.rar']
  const fileName = file.name.toLowerCase()
  const isValidType = allowedTypes.some(type => fileName.endsWith(type))
  
  if (!isValidType) {
    ElMessage.error('只支持PDF、Word、PPT、ZIP、RAR格式文件!')
    return false
  }
  
  workInfoForm.projectPlan = file.raw
  return false
}

const handleFileRemove = (file) => {
  workInfoForm.projectPlan = null
  fileList.value = []
}


const competitionTypeOptions = ref([])
const projectTypeOptions = ref([])
const teamSizeOptions = ref([])
const roleOptions = ref([])

const editOptions = [
  { key: 'participants', label: '参赛人员', icon: 'User', type: 'primary', disabled: false },
  { key: 'workInfo', label: '作品信息', icon: 'Document', type: 'success', disabled: false },
  { key: 'instructor', label: '指导老师', icon: 'User', type: 'warning', disabled: false }
]



const loadWorkList = async () => {
  try {
    loading.value = true
    
    const tokenInfo = localStorage.getItem('token_info')
    let userId = null
    
    if (tokenInfo) {
      try {
        const tokenData = JSON.parse(tokenInfo)
        userId = tokenData.userId
      } catch (e) {
        console.error('解析token_info失败:', e)
      }
    }

    if (!userId) {
      const sessionUser = sessionStorage.getItem('userInfo')
      if (sessionUser) {
        try {
          const userData = JSON.parse(sessionUser)
          userId = userData.id || userData.userId
        } catch (e) {
          console.error('解析sessionStorage用户信息失败:', e)
        }
      }
    }
    
    if (!userId) {
      ElMessage.error('用户信息获取失败，请重新登录')
      return
    }
    
    const params = {
      page: currentPage.value,
      size: pageSize.value
    }
    const response = await workDetailsApi.getWorkListByUserId(userId, params)
    worksData.value = workDataUtils.transformWorkList(response.data.records || response.data)
    total.value = response.data.total || response.data.length
  } catch (error) {
    ElMessage.error('获取作品列表失败')
    console.error('获取作品列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadCompetitionTypes = async () => {
  if (competitionTypeOptions.value.length > 0) return
  
  try {
    const response = await workDetailsApi.getCompetitionTypes()
    competitionTypeOptions.value = response.data || []
  } catch (error) {
    console.error('加载赛事类型失败:', error)
    ElMessage.warning('赛事类型加载失败')
  }
}

const loadProjectTypes = async () => {
  if (projectTypeOptions.value.length > 0) return
  
  try {
    const response = await workDetailsApi.getProjectTypes()
    projectTypeOptions.value = response.data || []
  } catch (error) {
    console.error('加载项目类型失败:', error)
    ElMessage.warning('项目类型加载失败')
  }
}

const loadTeamSizeOptions = async () => {
  if (teamSizeOptions.value.length > 0) return
  
  try {
    const response = await workDetailsApi.getTeamSizeOptions()
    teamSizeOptions.value = response.data || []
  } catch (error) {
    console.error('加载团队规模选项失败:', error)
    ElMessage.warning('团队规模选项加载失败')
  }
}

const loadRoleOptions = async () => {
  if (roleOptions.value.length > 0) return
  
  try {
    const response = await workDetailsApi.getRoleOptions()
    roleOptions.value = response.data || []
  } catch (error) {
    console.error('加载角色选项失败:', error)
    ElMessage.warning('角色选项加载失败')
  }
}

// 创建作品功能暂时注释
// const createNewWork = () => {
//   router.push('/competition/entry-details')
// }

const editWork = (work) => {
  selectedWork.value = work
  editDialogVisible.value = true
}

// 添加各个功能的loading状态
const participantsLoading = ref(false)
const workInfoLoading = ref(false)
const instructorLoading = ref(false)
const detailLoading = ref(false)

const viewWorkSummary = async (work) => {
  try {
    detailLoading.value = true
    const response = await workDetailsApi.getWorkDetail(work.id)
    selectedWork.value = workDataUtils.transformWorkDetail(response.data)
    detailDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取作品详情失败')
    console.error('获取作品详情失败:', error)
  } finally {
    detailLoading.value = false
  }
}

const handleEdit = (action) => {
  if (!selectedWork.value) return

  switch (action) {
    case 'participants':
      editParticipants(selectedWork.value)
      break
    case 'workInfo':
      editWorkInfo(selectedWork.value)
      break
    case 'instructor':
      editInstructor(selectedWork.value)
      break
  }
  
  editDialogVisible.value = false
}

const editParticipants = (work) => {

  participantsForm.authors = work.authors ? work.authors.map(author => ({
    name: author.name || author,
    role: author.role || 'member',
    studentId: author.studentId || ''
  })) : []
  participantsDialogVisible.value = true
}

const editWorkInfo = (work) => {
  loadCompetitionTypes()
  
  Object.assign(workInfoForm, {
    name: work.name,
    type: work.type, // 修正字段名
    abstract: work.abstract || '',
    projectPlan: work.projectPlan || null,
    videoLink: work.videoLink || '',
    videoCode: work.videoCode || ''
  })
  workInfoDialogVisible.value = true
}

const editInstructor = (work) => {
  loadRoleOptions()
  
  instructorForm.instructors = work.instructors ? work.instructors.map(instructor => ({
    name: instructor.name || instructor,
    role: instructor.role || 'instructor',
    unit: instructor.unit || '',
    contact: instructor.contact || ''
  })) : []
  instructorDialogVisible.value = true
}


const addAuthor = () => {
  participantsForm.authors.push({
    name: '',
    role: 'member',
    studentId: ''
  })
}

const removeAuthor = (index) => {
  participantsForm.authors.splice(index, 1)
}

const addInstructor = () => {
  instructorForm.instructors.push({
    name: '',
    role: 'instructor',
    unit: '',
    contact: ''
  })
}

const removeInstructor = (index) => {
  instructorForm.instructors.splice(index, 1)
}




const saveParticipants = async () => {
  try {
    await participantsFormRef.value.validate()
    
    participantsLoading.value = true
    const data = {
      authors: participantsForm.authors
    }
    await workDetailsApi.updateParticipants(selectedWork.value.id, data)
    ElMessage.success('参赛人员信息保存成功')
    participantsDialogVisible.value = false
    loadWorkList() // 刷新列表
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存参赛人员信息失败')
      console.error('保存参赛人员信息失败:', error)
    }
  } finally {
    participantsLoading.value = false
  }
}

const saveWorkInfo = async () => {
  try {
    await workInfoFormRef.value.validate()
    
    workInfoLoading.value = true
    const data = {
      name: workInfoForm.name,
      type: workInfoForm.type, // 修正字段名
      abstract: workInfoForm.abstract,
      projectPlan: workInfoForm.projectPlan,
      videoLink: workInfoForm.videoLink,
      videoCode: workInfoForm.videoCode
    }
    await workDetailsApi.updateWorkInfo(selectedWork.value.id, data)
    ElMessage.success('作品信息保存成功')
    workInfoDialogVisible.value = false
    loadWorkList() // 刷新列表
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存作品信息失败')
      console.error('保存作品信息失败:', error)
    }
  } finally {
    workInfoLoading.value = false
  }
}

const saveInstructor = async () => {
  try {
    await instructorFormRef.value.validate()
    
    instructorLoading.value = true
    const data = {
      instructors: instructorForm.instructors
    }
    await workDetailsApi.updateInstructors(selectedWork.value.id, data)
    ElMessage.success('指导老师信息保存成功')
    instructorDialogVisible.value = false
    loadWorkList() // 刷新列表
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存指导老师信息失败')
      console.error('保存指导老师信息失败:', error)
    }
  } finally {
    instructorLoading.value = false
  }
}

const submitWork = async (work) => {
  try {
    await ElMessageBox.confirm(
      `确定要提交作品"${work.name}"吗？提交后将无法修改。`,
      '确认提交',
      { type: 'warning' }
    )
    
    await workDetailsApi.submitWork(work.id)
    ElMessage.success('作品提交成功')
    loadWorkList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('提交作品失败')
      console.error('提交作品失败:', error)
    }
  }
}

const downloadWorkFiles = async (work) => {
  try {
    loading.value = true
    const response = await workDetailsApi.downloadWorkFiles(work.id)
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${work.name}_材料.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('材料下载成功')
  } catch (error) {
    ElMessage.error('下载材料失败')
    console.error('下载材料失败:', error)
  } finally {
    loading.value = false
  }
}

const shareWork = (work) => {
  const shareUrl = `${window.location.origin}/competition/work/${work.id}`
  navigator.clipboard.writeText(shareUrl).then(() => {
    ElMessage.success('作品链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制链接失败')
  })
}


const exitCompetition = async (work) => {
  try {
    await ElMessageBox.confirm(
      `确定要退出比赛"${work.name}"吗？退出后将无法恢复。`,
      '确认退出',
      { type: 'warning' }
    )
    
    await workDetailsApi.exitCompetition(work.id)
    ElMessage.success('已成功退出比赛')
    loadWorkList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('退出比赛失败')
      console.error('退出比赛失败:', error)
    }
  }
}

const deleteWork = async (work) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除作品"${work.name}"吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )
    
    await workDetailsApi.deleteWork(work.id)
    ElMessage.success('作品删除成功')
    loadWorkList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除作品失败')
      console.error('删除作品失败:', error)
    }
  }
}



const handleSelectionChange = (selection) => {
  console.log('选中的作品:', selection)
}

const handleSizeChange = (val) => {
  pageSize.value = val
  loadWorkList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadWorkList()
}

// 工具方法
const getTypeTagType = (type) => {
  return typeMap[type]?.type || 'info'
}

const getTypeLabel = (type) => {
  return typeMap[type]?.label || type
}

const getStatusTagType = (status) => {
  return statusMap[status]?.type || 'info'
}

const getStatusLabel = (status) => {
  return statusMap[status]?.label || status
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString()
}


onMounted(() => {
  const tokenInfo = localStorage.getItem('token_info')
  let isLoggedIn = false
  
  if (tokenInfo) {
    try {
      const tokenData = JSON.parse(tokenInfo)
      isLoggedIn = !!tokenData.userId && !!tokenData.accessToken
    } catch (e) {
      console.error('解析token_info失败:', e)
    }
  }

  if (!isLoggedIn) {
    const sessionUser = sessionStorage.getItem('userInfo')
    if (sessionUser) {
      try {
        const userData = JSON.parse(sessionUser)
        isLoggedIn = !!(userData.id || userData.userId)
      } catch (e) {
        console.error('解析sessionStorage用户信息失败:', e)
      }
    }
  }
  
  if (!isLoggedIn) {
    ElMessage.error('请先登录')
    router.push('/login')
    return
  }

  loadWorkList()
})

const handleRowCommand = (cmd, row) => {
  if (cmd === 'exit') {
    exitCompetition(row)
  } else if (cmd === 'delete') {
    deleteWork(row)
  }
}


const getCurrentUserId = () => {
  const tokenInfo = localStorage.getItem('token_info')
  if (tokenInfo) {
    try {
      const tokenData = JSON.parse(tokenInfo)
      return tokenData.userId || '未知'
    } catch (e) {
      console.error('解析token_info失败:', e)
    }
  }
  
  // 从sessionStorage获取
  const sessionUser = sessionStorage.getItem('userInfo')
  if (sessionUser) {
    try {
      const userData = JSON.parse(sessionUser)
      return userData.id || userData.userId || '未知'
    } catch (e) {
      console.error('解析sessionStorage用户信息失败:', e)
    }
  }
  
  return '未知'
}

const downloadFile = (file) => {
  try {
    if (!file || !file.url) {
      ElMessage.error('文件链接无效')
      return
    }
    
    const link = document.createElement('a')
    link.href = file.url || file.downloadUrl
    link.download = file.name || file.fileName || 'download'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('文件下载成功')
  } catch (error) {
    ElMessage.error('文件下载失败')
    console.error('文件下载失败:', error)
  }
}


</script>

<template>
  <div class="competition-work-page">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">作品管理</h1>
      </div>
      <div class="header-actions">
        <!-- 创建作品功能暂时注释 -->
        <!-- <el-button type="primary" @click="createNewWork" :icon="Plus">
          新建作品
        </el-button> -->
        <el-button @click="loadWorkList" :loading="loading" :icon="Search">
          刷新列表
        </el-button>
      </div>
    </div>


    <div class="works-table-container">
      <!-- 空状态提示 -->
      <div v-if="!loading && worksData.length === 0" class="empty-state">
        <el-empty 
          description="您还没有报名任何赛事，暂无作品信息"
          :image-size="200"
        >
          <el-button type="primary" @click="loadWorkList">刷新</el-button>
        </el-empty>
      </div>
      
      <el-table
        v-else
        :data="worksData"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        border
        class="works-table"
      >

          <el-table-column prop="competitionName" label="赛事名称" width="200">
            <template #header>
              <div class="column-header">
                <span>赛事名称</span>
                <el-icon class="filter-icon"><ArrowDown /></el-icon>
              </div>
            </template>
            <template #default="{ row }">
              <span class="competition-name">{{ row.competitionName }}</span>
            </template>
          </el-table-column>


          <el-table-column prop="competitionType" label="赛事类型" width="120">
            <template #header>
              <div class="column-header">
                <span>赛事类型</span>
                <el-icon class="filter-icon"><ArrowDown /></el-icon>
              </div>
            </template>
                          <template #default="{ row }">
                <el-tag :type="getTypeTagType(row.type)" size="small">
                  {{ getTypeLabel(row.type) }}
                </el-tag>
              </template>
          </el-table-column>


          <el-table-column prop="code" label="参赛编号" width="140">
            <template #header>
              <div class="column-header">
                <span>参赛编号</span>
                <el-icon class="search-icon"><Search /></el-icon>
              </div>
            </template>
            <template #default="{ row }">
              <span class="work-id">{{ row.code }}</span>
            </template>
          </el-table-column>


          <el-table-column prop="name" label="作品名称" min-width="300">
            <template #header>
              <div class="column-header">
                <span>作品名称</span>
                <el-icon class="search-icon"><Search /></el-icon>
              </div>
            </template>
            <template #default="{ row }">
              <div class="work-title">
                <span class="title-text">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>


          <el-table-column label="操作" width="180" fixed="right">
            <template #header>
              <div class="column-header">
                <span>操作</span>
              </div>
            </template>
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" type="success" @click="viewWorkSummary(row)" :loading="detailLoading">查看详情</el-button>
                <el-button size="small" type="primary" @click="editWork(row)">编辑作品</el-button>
                <el-dropdown @command="(cmd) => handleRowCommand(cmd, row)">
                  <el-button size="small" type="info">
                    操作<el-icon style="margin-left: 2px;"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="exit">退出比赛</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除作品</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>


        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
    </div>

    <el-dialog
      v-model="detailDialogVisible"
      title="作品信息汇总"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedWork" class="work-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="参赛编号">{{ selectedWork.code || '-' }}</el-descriptions-item>
          <el-descriptions-item label="作品名称">{{ selectedWork.name }}</el-descriptions-item>
                      <el-descriptions-item label="赛事类型">
              <el-tag :type="getTypeTagType(selectedWork.type)">
                {{ getTypeLabel(selectedWork.type) }}
              </el-tag>
            </el-descriptions-item>
          <el-descriptions-item label="作者">{{ selectedWork.authors?.map(author => author.name || author).join(', ') || '-' }}</el-descriptions-item>
          <el-descriptions-item label="指导教师">{{ selectedWork.instructors?.map(instructor => instructor.name || instructor).join(', ') || '-' }}</el-descriptions-item>
          <el-descriptions-item label="提交时间">{{ formatDate(selectedWork.submitTime) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="work-abstract-section" v-if="selectedWork.abstract">
          <h3>作品摘要</h3>
          <p>{{ selectedWork.abstract }}</p>
        </div>

        <div class="work-files-section" v-if="selectedWork.files?.length">
          <h3>作品材料</h3>
          <div class="file-list">
            <div v-for="file in selectedWork.files" :key="file.id" class="file-item">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ file.name }}</span>
              <el-button size="small" type="primary" @click="downloadFile(file)">下载</el-button>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>


    <el-dialog
      v-model="editDialogVisible"
      title="编辑作品"
      width="600px"
      :close-on-click-modal="false"
      class="edit-work-dialog"
    >
      <div v-if="selectedWork" class="edit-work-content">

        <div class="edit-options">
          <div class="edit-option" @click="handleEdit('participants')">
            <div class="option-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="option-content">
              <div class="option-title">参赛人员</div>
              <div class="option-desc">管理参赛学生信息</div>
            </div>
          </div>

          <div class="edit-option" @click="handleEdit('workInfo')">
            <div class="option-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="option-content">
              <div class="option-title">作品信息</div>
              <div class="option-desc">编辑作品基本资料和材料</div>
            </div>
          </div>

          <div class="edit-option" @click="handleEdit('instructor')">
            <div class="option-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="option-content">
              <div class="option-title">指导老师</div>
              <div class="option-desc">管理指导教师信息</div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>


    <el-dialog
      v-model="participantsDialogVisible"
      title="编辑参赛人员"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedWork" class="dialog-header-info">
        <el-tag :type="getStatusTagType(selectedWork.status)" size="small">
          {{ getStatusLabel(selectedWork.status) }}
        </el-tag>
        <span class="work-name">{{ selectedWork.name }}</span>
      </div>
      <div class="participants-form">
        <el-form 
          ref="participantsFormRef"
          :model="participantsForm"
          :rules="participantsRules"
          label-width="100px"
        >
        <div class="form-section">
          <div class="section-header">
            <h3>参赛人员信息</h3>
            <el-button type="primary" icon="Plus" size="small" @click="addAuthor">
              添加参赛人员
            </el-button>
          </div>
          <div v-for="(author, index) in participantsForm.authors" :key="index" class="author-item">
            <el-form-item label="姓名" :prop="'authors.' + index + '.name'">
              <el-input v-model="author.name" placeholder="参赛人员姓名" />
            </el-form-item>
            <el-form-item label="角色">
              <el-select v-model="author.role" class="small-select">
                <el-option label="队长" value="leader" />
                <el-option label="队员" value="member" />
              </el-select>
            </el-form-item>
            <el-form-item label="学号">
              <el-input v-model="author.studentId" placeholder="学号" />
            </el-form-item>
            <el-button type="danger" icon="Delete" circle @click="removeAuthor(index)" class="delete-button" />
          </div>
        </div>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="participantsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveParticipants" :loading="participantsLoading">保存</el-button>
      </template>
    </el-dialog>


    <el-dialog
      v-model="workInfoDialogVisible"
      title="编辑作品信息"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedWork" class="dialog-header-info">
        <el-tag :type="getStatusTagType(selectedWork.status)" size="small">
          {{ getStatusLabel(selectedWork.status) }}
        </el-tag>
        <span class="work-name">{{ selectedWork.name }}</span>
      </div>
      <div class="work-info-form">
        <el-form 
          ref="workInfoFormRef"
          :model="workInfoForm" 
          :rules="workInfoRules"
          label-width="120px"
        >

          <div class="form-section">
            <el-form-item label="作品名称">
              <el-input v-model="workInfoForm.name" placeholder="请输入作品名称" />
            </el-form-item>

            <el-form-item label="赛事类型">
              <el-select v-model="workInfoForm.type" placeholder="请选择赛事类型" class="full-width">
                <el-option
                  v-for="option in competitionTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>


          <div class="form-section">
            <div class="section-header">
              <h3>作品详情</h3>
            </div>
            <el-form-item label="作品简介">
              <el-input
                v-model="workInfoForm.abstract"
                type="textarea"
                :rows="8"
                placeholder="请详细介绍作品的背景、目标、内容、创新点、应用价值等"
              />
            </el-form-item>
          </div>


          <div class="form-section">
            <div class="section-header">
              <h3>材料上传</h3>
            </div>
            <el-form-item label="文件上传">
              <el-upload
                class="upload-demo"
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :file-list="fileList"
                accept=".pdf,.doc,.docx,.ppt,.pptx,.zip,.rar"
              >
                <el-button type="primary">选择文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持PDF、Word、PPT、ZIP、RAR等格式，文件大小不超过50MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>


          <div class="form-section">
            <div class="section-header">
              <h3>作品材料汇总</h3>
            </div>
            <el-form-item label="网盘链接" required>
              <el-input v-model="workInfoForm.videoLink" placeholder="请输入网盘链接" />
            </el-form-item>
            <el-form-item label="网盘提取码" required>
              <el-input v-model="workInfoForm.videoCode" placeholder="请输入4位提取码" />
            </el-form-item>
          </div>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="workInfoDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveWorkInfo" :loading="workInfoLoading">保存</el-button>
      </template>
    </el-dialog>


    <el-dialog
      v-model="instructorDialogVisible"
      title="编辑指导老师"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedWork" class="dialog-header-info">
        <el-tag :type="getStatusTagType(selectedWork.status)" size="small">
          {{ getStatusLabel(selectedWork.status) }}
        </el-tag>
        <span class="work-name">{{ selectedWork.name }}</span>
      </div>
      <div class="instructor-form">
        <el-form 
          ref="instructorFormRef"
          :model="instructorForm"
          :rules="instructorRules"
          label-width="100px"
        >
        <div class="form-section">
          <div class="section-header">
            <h3>指导老师信息</h3>
            <el-button type="primary" icon="Plus" size="small" @click="addInstructor">
              添加指导老师
            </el-button>
          </div>
          <div v-for="(instructor, index) in instructorForm.instructors" :key="index" class="instructor-item">
            <el-form-item label="姓名">
              <el-input v-model="instructor.name" placeholder="指导老师姓名" />
            </el-form-item>
            <el-form-item label="角色">
              <el-select v-model="instructor.role" class="small-select">
                <el-option label="指导教师" value="instructor" />
                <el-option label="领队" value="leader" />
                <el-option label="辅助教师" value="assistant" />
              </el-select>
            </el-form-item>
            <el-form-item label="单位">
              <el-input v-model="instructor.unit" placeholder="所属单位" />
            </el-form-item>
            <el-form-item label="联系方式">
              <el-input v-model="instructor.contact" placeholder="联系电话" />
            </el-form-item>
            <el-button type="danger" icon="Delete" circle @click="removeInstructor(index)" class="delete-button" />
          </div>
        </div>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="instructorDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveInstructor" :loading="instructorLoading">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.competition-work-page {
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-right: 24px;
  padding-left: 8px;
}

.page-title, .page-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
  letter-spacing: 1px;
  text-align: left;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
  margin: 4px 0 0 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.works-table {
  width: 100%;
  margin-top: 20px;
  font-size: 14px;
  color: #333333;
}

.works-table th {
  font-size: 16px;
  color: #222;
  background: #fafbfc;
}

.works-table :deep(.el-table__row) {
  transition: background 0.2s;
}

.works-table :deep(.el-table__row:hover) {
  background: #F5F5F5 !important;
}

.works-table td, .works-table th {
  padding: 10px 8px;
}

.works-table-container {
  background: transparent;
  border-radius: 4px;
  box-shadow: none;
  padding: 24px 20px 12px 20px;
  width: 100%;
  margin: 0;
  min-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dialog-header-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.work-name {
  font-weight: 500;
  color: #303133;
}

.column-header {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.filter-icon,
.search-icon {
  font-size: 12px;
  color: #909399;
}

.competition-name {
  font-weight: 500;
  color: #409eff;
}

.work-id {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #409eff;
}

.work-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-text {
  flex: 1;
  font-weight: 500;
}

.status-tag {
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

:deep(.el-button) {
  border-radius: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

:deep(.el-pagination) {
  font-size: 14px;
  color: #333333;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 4px;
}

.work-detail {
  max-height: 60vh;
  overflow-y: auto;
}

:deep(.el-dialog) {
  border-radius: 4px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

.work-abstract-section,
.work-files-section {
  margin-top: 24px;
}

.work-abstract-section h3,
.work-files-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.work-abstract-section p {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.file-name {
  flex: 1;
  color: #303133;
}


.edit-work-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.edit-work-content {
  padding: 0;
}

.edit-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.edit-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.edit-option:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f0f9ff;
  color: #409eff;
  font-size: 18px;
}

.edit-option:nth-child(2) .option-icon {
  background: #f0f9ff;
  color: #67c23a;
}

.edit-option:nth-child(3) .option-icon {
  background: #fff7e6;
  color: #e6a23c;
}

.edit-option:nth-child(4) .option-icon {
  background: #f0f9ff;
  color: #909399;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.option-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 弹窗表单样式 */
.participants-form,
.work-info-form,
.instructor-form {
  padding: 0;
}

.form-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.author-item,
.attachment-item,
.instructor-item {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.author-item .el-form-item,
.attachment-item .el-form-item,
.instructor-item .el-form-item {
  margin-bottom: 0;
  flex: 1;
  min-width: 180px;
}

.author-item .el-form-item:first-child,
.instructor-item .el-form-item:first-child {
  flex: 1;
  min-width: 180px;
}

.author-item .el-form-item .el-input,
.author-item .el-form-item .el-select,
.attachment-item .el-form-item .el-input,
.attachment-item .el-form-item .el-select,
.instructor-item .el-form-item .el-input,
.instructor-item .el-form-item .el-select {
  width: 100%;
}

.delete-button {
  margin-left: auto;
  flex-shrink: 0;
}

.small-select {
  width: 100%;
}



.upload-demo {
  margin-top: 10px;
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.uploaded-file-name {
  margin-left: 10px;
  color: #606266;
  font-size: 14px;
}

.attachment-name-input,
.attachment-description-input {
  flex-basis: calc(33% - 15px);
  max-width: calc(33% - 15px);
}

.upload-demo-small {
  margin-top: 0;
}

.full-width {
  width: 100%;
}


:deep(.el-card) {
  border-radius: 8px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-pagination) {
  justify-content: center;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .edit-options {
    grid-template-columns: 1fr;
  }
}
</style>