<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElTable, ElTableColumn, ElDialog, ElTag, ElButton, ElTimeline, ElTimelineItem, ElIcon, ElTooltip, ElPagination, ElInput } from 'element-plus';
import { DocumentCopy, View, Search, Plus, Share, Link, Picture } from '@element-plus/icons-vue';
import { getAppealResultListApi } from '../api/appeal';

const tableData = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const shareDialogVisible = ref(false);
const currentAppeal = ref(null);

const searchPhone = ref('');

const userInfo = ref({
  phone: '13888888888'
});

const filterStatus = ref('all');
const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '待审核', value: 'pending' },
  { label: '已通过', value: 'approved' },
  { label: '已驳回', value: 'rejected' },
];

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const appealStatusMap = {
  'pending_initial': { label: '待初审', type: 'warning', stage: '初审' },
  'pending_second': { label: '待复审', type: 'warning', stage: '复审' },
  'pending_final': { label: '待终审', type: 'warning', stage: '终审' },
  'approved': { label: '已通过', type: 'success', stage: '已完成' },
  'rejected': { label: '已驳回', type: 'danger', stage: '已驳回' },
};

const allAppealData = ref([]);


const appealStats = ref({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0
});


const calculateStats = (appeals) => {
  const stats = {
    total: appeals.length,
    pending: 0,
    approved: 0,
    rejected: 0
  };

  appeals.forEach(appeal => {
    if (appeal.status === 'approved') {
      stats.approved++;
    } else if (appeal.status === 'rejected') {
      stats.rejected++;
    } else {
      stats.pending++;
    }
  });

  return stats;
};


const fetchAppealResultList = async () => {
  loading.value = true;
  try {
    const params = {
      phone: userInfo.value.phone,
      status: filterStatus.value !== 'all' ? filterStatus.value : undefined,
      page: currentPage.value,
      pageSize: pageSize.value
    };

    const response = await getAppealResultListApi(params);
    if (response.data && response.data.code === 200) {
      allAppealData.value = response.data.data.list;
      total.value = response.data.data.total;

      appealStats.value = calculateStats(allAppealData.value);
    } else {
      ElMessage.error(response.data.message || '获取申诉结果列表失败');
    }
  } catch (error) {
    console.error('获取申诉结果列表失败:', error);
    ElMessage.error('获取申诉结果列表失败');
  } finally {
    loading.value = false;
  }
};


const filteredData = computed(() => {
  let data = allAppealData.value;

  if (filterStatus.value !== 'all') {
    data = data.filter(appeal => appeal.status === filterStatus.value);
  }

  if (searchPhone.value) {
    const searchTerm = searchPhone.value.toLowerCase();
    data = data.filter(appeal => appeal.submitterPhone && appeal.submitterPhone.toLowerCase().includes(searchTerm));
  }

  return data;
});

// 分页数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredData.value.slice(start, end);
});

// 处理筛选和搜索变化
const handleFilterOrSearchChange = () => {
  currentPage.value = 1;
  total.value = filteredData.value.length;
};

const handleFilterChange = () => {
  handleFilterOrSearchChange();
};

const handleSearchInput = () => {
  handleFilterOrSearchChange();
};


const handlePageChange = (newPage) => {
  currentPage.value = newPage;
};

const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  currentPage.value = 1;
};


const openAppealDialog = (row) => {
  currentAppeal.value = row;
  dialogVisible.value = true;
};


const getReviewStageInfo = (appeal, stage) => {
  if (!appeal.reviews) return '';
  const review = appeal.reviews.find(r => r.stage === stage);
  if (!review) return '待审核';
  return review.status.includes('approved') ? '已通过' : '已驳回';
};


const formatDate = (date) => {
  if (!date) return '';
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return date;
  return dateObj.toLocaleString();
};


const getStatusType = (status) => {
  const statusInfo = appealStatusMap[status];
  return statusInfo ? statusInfo.type : 'info';
};


const getStatusLabel = (status) => {
  const statusInfo = appealStatusMap[status];
  return statusInfo ? statusInfo.label : status;
};


const getCurrentStage = (status) => {
  const statusInfo = appealStatusMap[status];
  return statusInfo ? statusInfo.stage : '';
};

const getLatestReviewComment = (reviews) => {
  if (!reviews || reviews.length === 0) return '暂无审核意见';
  const latestReview = reviews[reviews.length - 1];
  return latestReview.comment || '暂无审核意见';
};


const copyToClipboard = (text, message) => {
  if (!navigator.clipboard) {
    ElMessage.error('浏览器不支持复制到剪贴板功能');
    return;
  }
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success(message || '复制成功');
  }).catch(err => {
    console.error('复制失败:', err);
    ElMessage.error('复制失败');
  });
};


const getAppealProgress = (appeal) => {
  const stages = ['initial', 'second', 'final'];
  const currentStage = appeal.status.split('_')[1] || 'completed';
  const progress = {
    current: 0,
    total: stages.length,
    stage: currentStage
  };

  if (currentStage === 'completed') {
    progress.current = stages.length;
  } else {
    progress.current = stages.indexOf(currentStage) + 1;
  }

  return progress;
};

// 跳转到申诉提交页面
const goToAppealSubmit = () => {
  // 使用路由导航到申诉提交页面
  window.location.href = '/competition/CompetitionAppeal';
};

const openShareDialog = () => {
  shareDialogVisible.value = true;
};

const handleShare = (type) => {
  ElMessage.info('分享功能开发中，敬请期待！');
  shareDialogVisible.value = false;
};

onMounted(() => {
  fetchAppealResultList();
});
</script>

<template>
  <div class="page">
    <div class="result-card">
      <div class="page-header">
        <h1>我的申诉</h1>
        <el-button type="primary" @click="goToAppealSubmit">
          <el-icon><Plus /></el-icon>
          提交新申诉
        </el-button>
      </div>

      <!-- 申诉统计卡片 -->
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-value">{{ appealStats.total }}</div>
          <div class="stat-label">总申诉数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ appealStats.pending }}</div>
          <div class="stat-label">待处理</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ appealStats.approved }}</div>
          <div class="stat-label">已通过</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ appealStats.rejected }}</div>
          <div class="stat-label">已驳回</div>
        </div>
      </div>

      <!-- 筛选控件 -->
      <div class="controls-container">
        <div class="filter-controls">
          <label style="margin-right: 10px;">筛选状态:</label>
          <el-radio-group v-model="filterStatus" @change="handleFilterChange">
            <el-radio-button
              v-for="option in filterOptions"
              :key="option.value"
              :label="option.value"
            >{{ option.label }}</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 申诉列表 -->
      <el-table
        :data="paginatedData"
        v-loading="loading"
        style="width: 100%"
        border
        empty-text="暂无申诉记录～"
      >
        <el-table-column prop="id" label="申诉ID" width="80" />
        <el-table-column prop="applicationType" label="申诉类型" width="120">
          <template #default="{ row }">
            {{ row.applicationType === 'player_complaint' ? '选手投诉' :
              row.applicationType === 'player_appeal' ? '选手申诉' :
              row.applicationType === 'team_complaint' ? '参赛队投诉' :
              row.applicationType === 'team_appeal' ? '参赛队申诉' :
              row.applicationType === 'process_appeal' ? '流程申诉' : '其他申诉' }}
          </template>
        </el-table-column>
        <el-table-column label="审核进度" width="300">
          <template #default="{ row }">
            <div class="progress-cell">
              <el-steps :active="getAppealProgress(row).current" finish-status="success" simple>
                <el-step title="初审" />
                <el-step title="复审" />
                <el-step title="终审" />
              </el-steps>
              <div class="stage-info">{{ getCurrentStage(row.status) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="审核结果" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="最新审核意见" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="review-comment">
              {{ getLatestReviewComment(row.reviews) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="View"
              @click="openAppealDialog(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        background
        style="margin-top: 20px; text-align: right;"
      />

      <!-- 申诉详情对话框 -->
      <el-dialog
        v-model="dialogVisible"
        title="申诉详情"
        width="60%"
        :close-on-click-modal="false"
      >
        <div v-if="currentAppeal" class="appeal-detail-content">
          <!-- 申诉进度条 -->
          <div class="appeal-progress">
            <el-steps :active="getAppealProgress(currentAppeal).current" finish-status="success">
              <el-step title="初审" :description="getReviewStageInfo(currentAppeal, 'initial')" />
              <el-step title="复审" :description="getReviewStageInfo(currentAppeal, 'second')" />
              <el-step title="终审" :description="getReviewStageInfo(currentAppeal, 'final')" />
            </el-steps>
          </div>

          <!-- 审核历史 -->
          <div v-if="currentAppeal.reviews && currentAppeal.reviews.length > 0" class="review-history-section">
            <h3>审核历史</h3>
            <el-timeline>
              <el-timeline-item
                v-for="(review, index) in currentAppeal.reviews"
                :key="index"
                :timestamp="formatDate(review.reviewTime)"
                placement="top"
                :type="getStatusType(review.status)"
              >
                <h4>{{ review.stage === 'initial' ? '初审' : review.stage === 'second' ? '复审' : '终审' }}</h4>
                <p><strong>结果:</strong> <el-tag :type="getStatusType(review.status)">{{ getStatusLabel(review.status) }}</el-tag></p>
                <p v-if="review.status.includes('rejected')" class="rejection-reason">
                  <strong>驳回原因:</strong> {{ review.comment || '无' }}
                </p>
              </el-timeline-item>
            </el-timeline>
          </div>

          <!-- 添加分享按钮 -->
          <div class="share-section">
            <el-button type="primary" @click="openShareDialog">
              <el-icon><Share /></el-icon>
              分享申诉
            </el-button>
          </div>
        </div>
      </el-dialog>

      <!-- 分享对话框 -->
      <el-dialog
        v-model="shareDialogVisible"
        title="分享申诉"
        width="400px"
        :close-on-click-modal="false"
      >
        <div class="share-options">
          <div class="share-option" @click="handleShare('link')">
            <el-icon><Link /></el-icon>
            <span>复制链接</span>
          </div>
          <div class="share-option" @click="handleShare('qrcode')">
            <el-icon><Picture /></el-icon>
            <span>二维码分享</span>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<style scoped>
.page {
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  background: #fff;
  border: 1px solid #dcdfe6;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.progress-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stage-info {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.appeal-progress {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.review-history-section {
  margin-top: 24px;
}

.rejection-reason {
  color: #F56C6C;
  background-color: #FEF0F0;
  padding: 12px;
  border-radius: 4px;
  margin-top: 8px;
}

.question-section {
  margin-top: 24px;
  text-align: center;
}

:deep(.el-steps--simple) {
  background: transparent;
  padding: 0;
}

:deep(.el-step__title) {
  font-size: 13px;
}

:deep(.el-step__description) {
  font-size: 12px;
  color: #909399;
}

:deep(.el-timeline-item__node) {
  z-index: 1;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 15px;
}

:deep(.el-timeline-item__content) {
  font-size: 14px;
  color: #555;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 13px;
  color: #909399;
}

.controls-container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.search-area {
  display: flex;
  align-items: center;
}

.filter-controls {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.appeal-detail-content {
  padding: 0 20px 20px;
}

.appeal-info,
.review-history-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 80px;
}

.info-item span {
  color: #333;
  word-break: break-all;
  flex-grow: 1;
}

.info-item .el-button {
  margin-left: 8px;
  padding: 0 5px;
  height: auto;
}

.review-comment {
  color: #606266;
  line-height: 1.4;
}

.share-section {
  margin-top: 20px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.share-options {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 20px;
  border-radius: 8px;
  transition: all 0.3s;
}

.share-option:hover {
  background-color: #f5f7fa;
}

.share-option .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #409EFF;
}

.share-option span {
  font-size: 14px;
  color: #606266;
}
</style> 