<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElCollapse, ElCollapseItem, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElIcon, ElRow, ElCol, ElDrawer, ElTable, ElTableColumn, ElTag, ElSteps, ElStep } from 'element-plus';
import { View } from '@element-plus/icons-vue';
import { submitAppealApi, getAppealStatusListApi } from '../api/appeal';
import ProUploadFile from './CommonUpload/ProUploadFile.vue';
import { useRouter } from 'vue-router';

const appealForm = reactive({
  applicationType: '',
  workId: '',
  submittingUnit: '',
  request: '',
  explanation: '',
  evidenceLink: '',
  extractionCode: '',
  submitter: '',
  submitterUnit: '',
  submitterPhone: '',
  submitterEmail: '',
  supportingMaterials: [],
});


const loading = ref(false);


const applicationTypes = [
  { label: '请选择', value: '' },
  { label: '选手投诉', value: 'player_complaint' },
  { label: '选手申诉', value: 'player_appeal' },
  { label: '参赛队投诉', value: 'team_complaint' },
  { label: '参赛队申诉', value: 'team_appeal' },
  { label: '流程申诉', value: 'process_appeal' },
  { label: '其他申诉', value: 'other_appeal' },
];

// 表单验证规则
const rules = {
  applicationType: [{ required: true, message: '请选择申诉类型', trigger: 'change' }],
  request: [{ required: true, message: '请填写您的诉求', trigger: 'blur' }],
  explanation: [{ required: true, message: '请填写详细说明', trigger: 'blur' }],
  evidenceLink: [{ required: true, message: '请输入网盘链接', trigger: 'blur' }],
  extractionCode: [
    { required: true, message: '请输入提取码', trigger: 'blur' },
    { len: 4, message: '提取码必须是4位', trigger: 'blur' }
  ],
  supportingMaterials: [],
  submitter: [{ required: true, message: '请输入提交人姓名', trigger: 'blur' }],
  submitterUnit: [{ required: true, message: '请输入提交人单位', trigger: 'blur' }],
  submitterPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/, message: '请输入正确的手机号码格式', trigger: 'blur' }
  ],
  submitterEmail: [
    { required: true, message: '请输入联系信箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
};


const appealFormRef = ref(null);

const DRAFT_KEY = 'competition_appeal_draft';
const router = useRouter();


const submitAppeal = async () => {
  if (!appealFormRef.value) {
    return;
  }

  try {
    await appealFormRef.value.validate();
    loading.value = true;
    try {
      const payload = {
        ...appealForm,
        supportingMaterials: Array.isArray(appealForm.supportingMaterials)
          ? appealForm.supportingMaterials.map(f => f.url || f).join(',')
          : (appealForm.supportingMaterials || '')
      }
      const response = await submitAppealApi(payload);

      if (response && response.code === 200) {
        ElMessage.success('提交成功，您可在 进度查询 中查看审核进度');
        localStorage.removeItem(DRAFT_KEY);
      } else {
        const errorMessage = response && response.msg ? response.msg : '提交失败，请稍后重试';
        ElMessage.error(errorMessage);
        console.error('申诉提交失败:', response);
      }
    } catch (error) {
      console.error('调用 submitAppealApi 失败:', error);
      ElMessage.error('提交失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  } catch (error) {
    console.error('表单验证失败:', error);
    ElMessage.error('请检查表单填写是否正确。');
  }
};


const saveDraft = () => {
  const draftData = {
    ...appealForm,
    supportingMaterials: appealForm.supportingMaterials,
    timestamp: Date.now()
  };
  localStorage.setItem(DRAFT_KEY, JSON.stringify(draftData));
  ElMessage.success('草稿已保存');
};

const loadDraft = () => {
  const draft = localStorage.getItem(DRAFT_KEY);
  if (draft) {
    try {
      const data = JSON.parse(draft);
      const now = Date.now();
      const sevenDays = 7 * 24 * 60 * 60 * 1000;
      if (data.timestamp && now - data.timestamp > sevenDays) {
        localStorage.removeItem(DRAFT_KEY);
        ElMessage.info('草稿已过期，已自动清除');
      } else {
        Object.assign(appealForm, data);
      }
    } catch (e) {
      // ignore
    }
  }
};

onMounted(() => {
  loadDraft();
});

const resetForm = () => {
  Object.assign(appealForm, {
    applicationType: '',
    workId: '',
    submittingUnit: '',
    request: '',
    explanation: '',
    evidenceLink: '',
    extractionCode: '',
    submitter: '',
    submitterUnit: '',
    submitterPhone: '',
    submitterEmail: '',
    supportingMaterials: [],
  });

  if (appealFormRef.value) {
    appealFormRef.value.clearValidate();
  }
  localStorage.removeItem(DRAFT_KEY);
};


const activeCollapseItems = ref(['notice']);

const showDrawer = ref(false);
const appealList = ref([]);
const drawerLoading = ref(false);


let userId = '';
try {
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));
  userId = userInfo?.userId || '';
} catch (e) {
  userId = '';
}


const fetchAppealList = async () => {
  drawerLoading.value = true;
  try {
    const response = await getAppealStatusListApi();
    if (response && response.code === 200) {
      appealList.value = response.data || [];
    } else {
      appealList.value = [];
    }
  } catch (e) {
    appealList.value = [];
  } finally {
    drawerLoading.value = false;
  }
};

const appealStatusMap = {
  'pending_initial': { label: '待初审', type: 'warning', stage: '初审' },
  'pending_second': { label: '待复审', type: 'warning', stage: '复审' },
  'pending_final': { label: '待终审', type: 'warning', stage: '终审' },
  'approved': { label: '已通过', type: 'success', stage: '已完成' },
  'rejected': { label: '已驳回', type: 'danger', stage: '已驳回' },
};

const getStatusType = (status) => {
  const statusInfo = appealStatusMap[status];
  return statusInfo ? statusInfo.type : 'info';
};
const getStatusLabel = (status) => {
  const statusInfo = appealStatusMap[status];
  return statusInfo ? statusInfo.label : status;
};
const getCurrentStage = (status) => {
  const statusInfo = appealStatusMap[status];
  return statusInfo ? statusInfo.stage : '';
};
const getAppealProgress = (appeal) => {
  const stages = ['initial', 'second', 'final'];
  const currentStage = appeal.status.split('_')[1] || 'completed';
  const progress = {
    current: 0,
    total: stages.length,
    stage: currentStage
  };
  if (currentStage === 'completed') {
    progress.current = stages.length;
  } else {
    progress.current = stages.indexOf(currentStage) + 1;
  }
  return progress;
};
const getLatestReviewComment = (reviews) => {
  if (!reviews || reviews.length === 0) return '暂无审核意见';
  const latestReview = reviews[reviews.length - 1];
  return latestReview.comment || '暂无审核意见';
};

const goToAppealResult = () => {
  showDrawer.value = true;
  fetchAppealList();
};

</script>

<template>
  <div class="page">
    <div class="appeal-card">
      <div class="page-header">
        <h1>赛事申诉</h1>
        <el-button type="primary" @click="goToAppealResult" style="margin-left: 24px; margin-right: 8px;">
          <el-icon><View /></el-icon>
          申诉进度查询
        </el-button>
      </div>
      <el-drawer v-model="showDrawer" title="申诉进度查询" direction="rtl" size="60%">
        <el-table 
          :data="appealList" 
          border 
          style="width: 100%"
          :empty-text="drawerLoading ? '加载中...' : '暂无申诉记录～'"
          v-loading="drawerLoading"
          class="appeal-progress-table"
        >
          <el-table-column prop="id" label="申诉ID" width="80" align="right" />
          <el-table-column prop="applicationType" label="申诉类型" width="120" align="left">
            <template #default="{ row }">
              {{ row.applicationType === 'player_complaint' ? '选手投诉' :
                row.applicationType === 'player_appeal' ? '选手申诉' :
                row.applicationType === 'team_complaint' ? '参赛队投诉' :
                row.applicationType === 'team_appeal' ? '参赛队申诉' :
                row.applicationType === 'process_appeal' ? '流程申诉' : '其他申诉' }}
            </template>
          </el-table-column>
          <el-table-column label="审核进度" width="220" align="right">
            <template #default="{ row }">
              <div class="progress-cell">
                <el-steps :active="getAppealProgress(row).current" finish-status="success" simple>
                  <el-step title="初审" />
                  <el-step title="复审" />
                  <el-step title="终审" />
                </el-steps>
                <div class="stage-info">{{ getCurrentStage(row.status) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="审核结果" width="120" align="right">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="最新审核意见" min-width="180" show-overflow-tooltip align="left">
            <template #default="{ row }">
              <div class="review-comment">
                {{ getLatestReviewComment(row.reviews) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-drawer>


      <el-collapse v-model="activeCollapseItems">
        <el-collapse-item title="申诉须知" name="notice">
          <p>1. 个人提出的申诉，须写明本人的真实姓名、所在单位、通信地址、联系手机号码、电子邮件地址等，并需提交身份证复印件和具有本人亲笔签名的申诉书。</p>
          <p>2. 单位提出的申诉，须写明联系人的真实姓名、所在单位、通信地址、联系手机号码、电子邮件地址等，并需提交加盖本单位公章和负责人亲笔签名的申诉书。</p>
          <p>3. 智芯云赛事管理系统组委会纪监委将对提出申诉的个人或单位的信息负有保密职责。最终解释权归智芯云赛事管理系统所有。</p>
          <p>4. 与异议有关的学校的相关部门，应协助智芯云赛事管理系统组委会纪监委对异议作品进行调查。</p>
        </el-collapse-item>
      </el-collapse>

      <el-form :model="appealForm" :rules="rules" ref="appealFormRef" label-width="140px" class="appeal-form">
        <h2>赛事结果申诉</h2>
        <el-form-item label="您要申请:" prop="applicationType">
          <el-select v-model="appealForm.applicationType" placeholder="请选择您要申请的类型" style="width: 80%;">
            <el-option v-for="type in applicationTypes" :key="type.value" :label="type.label" :value="type.value"/>
          </el-select>
        </el-form-item>

        <el-form-item label="作品编号:" prop="workId">
          <el-input v-model="appealForm.workId" placeholder="请输入作品编号" style="width: 80%"></el-input>
        </el-form-item>

        <el-form-item label="作品参赛单位:" prop="submittingUnit">
          <el-input v-model="appealForm.submittingUnit" placeholder="请输入作品参赛单位" style="width: 80%"></el-input>
        </el-form-item>

        <el-form-item label="您的诉求:" prop="request">
          <el-input type="textarea" :rows="3" v-model="appealForm.request" placeholder="请填写您的诉求" style="width: 80%"></el-input>
        </el-form-item>

        <el-form-item label="详细说明:" prop="explanation">
          <el-input type="textarea" :rows="5" v-model="appealForm.explanation" placeholder="请填写详细的情况说明" style="width: 80%"></el-input>
          <small>请填写详细的情况说明。如果是举报参赛作品的违规，还要提供可核查的证据。</small>
        </el-form-item>

        <h2 style="margin-top: 30px; margin-bottom: 24px; color: #333; letter-spacing: 1px; font-size: 20px; font-weight: 600; text-align: left;">证据提交</h2>
        <el-form-item label="(网盘链接):" prop="evidenceLink">
          <el-input v-model="appealForm.evidenceLink" placeholder="请输入网盘链接" style="width: 80%"></el-input>
        </el-form-item>
        <el-form-item label="(网盘提取码):" prop="extractionCode">
          <el-input v-model="appealForm.extractionCode" placeholder="请输入4位提取码" maxlength="4" style="width: 80%"></el-input>
        </el-form-item>

        <el-form-item label="证明材料:" prop="supportingMaterials">
          <div style="width: 80%;">
            <ProUploadFile
              v-model="appealForm.supportingMaterials"
              action="/api/competition/appeal/upload_attachment"
              :limit="1"
              :multiple="false"
              :show-file-list="true"
              accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.doc,.docx"
              tip="支持jpg/jpeg/png/pdf/zip/rar/doc/docx格式,大小不超过100MB"
              :max-size="100"
            />
            <div style="color: #888; font-size: 14px; margin-top: 8px; word-break: break-all;">
              申请与投诉：请实名撰写申请书、打印签字（作者与指导老师）、盖学校公章或教务处章，拍照后上传，并保存原件备查
            </div>
          </div>
        </el-form-item>

        <h2>提交人信息</h2>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="提交人:" prop="submitter">
              <el-input v-model="appealForm.submitter" placeholder="请输入提交人姓名" style="width: 90%"></el-input>
              <small>申诉与投诉请实名，举报建议实名。</small>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提交人单位:" prop="submitterUnit">
              <el-input v-model="appealForm.submitterUnit" placeholder="请输入提交人单位" style="width: 90%"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="提交人电话:" prop="submitterPhone">
              <el-input v-model="appealForm.submitterPhone" placeholder="请输入联系电话" style="width: 90%"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提交人信箱:" prop="submitterEmail">
              <el-input v-model="appealForm.submitterEmail" placeholder="请输入联系信箱" style="width: 90%"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" @click="submitAppeal" :loading="loading">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="saveDraft" type="info">保存草稿</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped>

.page {
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-right: 24px;
  padding-left: 8px;
}

.appeal-card {
  background: transparent;
  border-radius: 4px;
  box-shadow: none;
  padding: 24px 20px 12px 20px;
  width: 100%;
  margin: 0;
  min-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
}

h1 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  letter-spacing: 1px;
  text-align: left;
}

h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 30px 0 24px;
  color: #333;
  letter-spacing: 1px;
  text-align: left;
}


.appeal-form {
  margin-top: 0;
}

.appeal-form small {
  display: block;
  margin-top: 5px;
  color: #888;
  font-size: 12px;
  line-height: 1.5;
}


.el-collapse {
  margin-bottom: 24px;
  border-top: none;
  border-bottom: none;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5 !important;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}

.el-collapse-item__content p {
  margin-bottom: 10px;
  line-height: 1.6;
  color: #555;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  color: #555;
}

.appeal-progress-table {
  font-size: 14px;
  color: #333333;
}

.appeal-progress-table th {
  font-size: 16px;
  color: #222;
  background: #fafbfc;
}

.appeal-progress-table :deep(.el-table__row) {
  transition: background 0.2s;
}
.appeal-progress-table :deep(.el-table__row:hover) {
  background: #F5F5F5 !important;
}

.appeal-progress-table td, .appeal-progress-table th {
  padding: 10px 8px;
}

.progress-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.stage-info {
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.review-comment {
  color: #333333;
  font-size: 14px;
}
</style>