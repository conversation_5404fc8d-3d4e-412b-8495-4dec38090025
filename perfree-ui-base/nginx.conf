
error_log /var/log/nginx/error.log;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    include mime.types;
    server {
        listen       80 default_server;
        server_name  localhost;

        gzip on;
        gzip_min_length 1k;
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types text/plain application/x-javascript text/css application/xml application/javascript;
        gzip_proxied any;
        client_max_body_size 500m;

        location / { ## 前端项目
            root   /usr/share/nginx/dist/;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location /api/ {
            proxy_pass http://${SERVER_ADDR_IP}:${SERVER_ADDR_PORT}/api/; ## 后端项目地址
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}
