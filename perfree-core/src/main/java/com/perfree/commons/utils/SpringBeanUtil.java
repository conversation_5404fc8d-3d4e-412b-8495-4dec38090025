package com.perfree.commons.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class Spring<PERSON><PERSON><PERSON><PERSON> implements ApplicationContextAware {

    public static ConfigurableApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (applicationContext instanceof ConfigurableApplicationContext) {
            context = (ConfigurableApplicationContext) applicationContext;
        } else {
            throw new IllegalStateException("ApplicationContext is not a ConfigurableApplicationContext");
        }
    }
}
