package com.perfree.exception;

import com.perfree.commons.common.CommonResult;
import com.perfree.commons.enums.ResultCodeEnum;
import com.perfree.commons.exception.ServiceException;
import com.perfree.demoModel.DemoModelException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 定义全局异常处理
 * @date 15:37 2023/9/28
 */
@ControllerAdvice
public class GlobalExceptionHandler{
    private final static Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);



    @ExceptionHandler(NoResourceFoundException.class)
    @ResponseBody
    public void handleNoResourceFoundException(NoResourceFoundException exception) {
        LOGGER.error(exception.getMessage());
    }
    /**
     * <AUTHOR>
     * @description MethodArgumentNotValidException
     * @date 15:37 2023/9/28
     * @param exception exception
     * @return com.perfree.commons.common.CommonResult<?>
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public CommonResult<?> validationBodyException(MethodArgumentNotValidException exception) {
        BindingResult result = exception.getBindingResult();
        StringBuilder errorMessage = new StringBuilder();

        // 收集所有字段错误信息
        if (result.hasFieldErrors()) {
            result.getFieldErrors().forEach(fieldError -> {
                String fieldName = getFieldDisplayName(fieldError.getField());
                String message = fieldError.getDefaultMessage();
                if (errorMessage.length() > 0) {
                    errorMessage.append("；");
                }
                errorMessage.append(fieldName).append("：").append(message);
            });
        }

        String finalMessage = errorMessage.length() > 0 ? errorMessage.toString() : "参数验证失败";
        LOGGER.error("参数验证失败: {}", finalMessage);
        return CommonResult.error(ResultCodeEnum.FAIL.getCode(), finalMessage);
    }

    /**
     * 获取字段的中文显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        return switch (fieldName) {
            case "applicationType" -> "申诉类型";
            case "workId" -> "作品编号";
            case "submittingUnit" -> "作品参赛单位";
            case "request" -> "申诉诉求";
            case "explanation" -> "详细说明";
            case "evidenceLink" -> "网盘链接";
            case "extractionCode" -> "网盘提取码";
            case "supportingMaterials" -> "证明材料";
            case "submitter" -> "提交人姓名";
            case "submitterUnit" -> "提交人单位";
            case "submitterPhone" -> "联系电话";
            case "submitterEmail" -> "联系邮箱";
            default -> fieldName;
        };
    }

    /**
     * <AUTHOR>
     * @description HttpMessageConversionException
     * @date 15:37 2023/9/28
     * @param exception HttpMessageConversionException
     * @return com.perfree.commons.common.CommonResult<?>
     */
    @ExceptionHandler(HttpMessageConversionException.class)
    @ResponseBody
    public CommonResult<?> parameterTypeException(HttpMessageConversionException exception) {
        LOGGER.error(exception.getMessage(), exception);
        return CommonResult.error(ResultCodeEnum.FAIL.getCode(), exception.getMessage());
    }

    /**
     * <AUTHOR>
     * @description BindException
     * @date 15:38 2023/9/28
     * @param e BindException
     * @return com.perfree.commons.common.CommonResult<?>
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public CommonResult<?> handleBindException(BindException e) {
        StringBuilder errorMessage = new StringBuilder();

        // 收集所有字段错误信息
        e.getFieldErrors().forEach(fieldError -> {
            String fieldName = getFieldDisplayName(fieldError.getField());
            String message = fieldError.getDefaultMessage();
            if (errorMessage.length() > 0) {
                errorMessage.append("；");
            }
            errorMessage.append(fieldName).append("：").append(message);
        });

        String finalMessage = errorMessage.length() > 0 ? errorMessage.toString() : "参数错误";
        LOGGER.error("参数绑定失败: {}", finalMessage);
        return CommonResult.error(ResultCodeEnum.FAIL.getCode(), finalMessage);
    }

    /**
     * <AUTHOR>
     * @description 业务相关异常
     * @date 15:38 2023/9/28
     * @param exception ServiceException
     * @return com.perfree.commons.common.CommonResult<?>
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    public CommonResult<?> handleServiceException(ServiceException exception) {
        LOGGER.error(exception.getMessage(),exception);
        return CommonResult.error(exception.getCode(), exception.getMessage());
    }

    @ExceptionHandler(DemoModelException.class)
    public ResponseEntity<Object> handleDemoModelException(DemoModelException exception) {
        LOGGER.error(exception.getMessage(),exception);
        return new ResponseEntity<>(CommonResult.error(ResultCodeEnum.FAIL.getCode(), exception.getMessage()), HttpStatus.OK);
    }

    @ExceptionHandler(AccessDeniedException.class)
    @ResponseBody
    public CommonResult<?> handleAccessDeniedException(Exception exception) {
        LOGGER.error(exception.getMessage(), exception);
        return CommonResult.error(ResultCodeEnum.AUTH_FORBIDDEN.getCode(), ResultCodeEnum.AUTH_FORBIDDEN.getMsg());
    }

    @ExceptionHandler(value = IOException.class)
    public ResponseEntity<Object> handleIOException(IOException ex) {
        if (ex.getMessage().contains("Connection reset by peer")) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        LOGGER.error(ex.getMessage(), ex);
        return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public CommonResult<?> handleException(Exception exception) {
        LOGGER.error(exception.getMessage(), exception);
        return CommonResult.error(ResultCodeEnum.FAIL.getCode(), exception.getMessage());
    }
}
